<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yunqu.yc</groupId>
        <artifactId>yc-project</artifactId>
        <version>1.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>yc-api</artifactId>
    <packaging>war</packaging>
    <version>3.4.0</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <finalName>${project.artifactId}</finalName>
        <sourceDirectory>src/main/java</sourceDirectory>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <webResources>
                        <!--打包源码到war-->
                        <resource>
                            <directory>src</directory>
                            <targetPath>WEB-INF/src</targetPath>
                            <includes>
                                <include>**/*.java</include>
                            </includes>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.yunqu.mars.plugin</groupId>
                <artifactId>console-upgrade</artifactId>
                <version>1.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-version</goal>
                            <goal>push-mars</goal>
                        </goals>
                        <configuration>
                            <cleanLibDirectory>true</cleanLibDirectory>
                            <!--需要保留到lib下的jar包，支持通配符*-->
                            <!--<jarPatterns>thumbnailator*</jarPatterns>-->
                            <!--部署到服务器-->
                            <enableWarPushMars>true</enableWarPushMars>
                            <consoleUrl>http://172.16.85.100:9059/easitline-console</consoleUrl>
                            <!--账号密码，默认账号密码不用配置-->
                            <consoleUsername></consoleUsername>
                            <consolePassword></consolePassword>
                            <primaryVersion>3.3.0</primaryVersion>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>