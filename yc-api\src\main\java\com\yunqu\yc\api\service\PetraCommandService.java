package com.yunqu.yc.api.service;

import java.sql.SQLException;



import java.util.Base64;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import com.yunqu.yc.api.executor.EventDispatcher;
import com.yunqu.yc.api.executor.impl.TaskResultExecutor;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.activemq.MessageException;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.alarm.MarsAlarm;
import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.base.QueryFactory;
import com.yunqu.yc.api.context.EntContext;
import com.yunqu.yc.api.log.DBLogger;
import com.yunqu.yc.api.log.ApiLogger;
import com.yunqu.yc.api.log.MonitorLogger;
import com.yunqu.yc.api.log.OCSLogger;
import com.yunqu.yc.api.log.PetraLogger;
import com.yunqu.yc.api.log.TaskLogger;
import com.yunqu.yc.api.model.RequestDataModel;
import com.yunqu.yc.api.model.ResponseDataModel;
import com.yunqu.yc.api.task.TaskResultHandler;
import com.yunqu.yc.api.util.CacheUtil;
import com.yunqu.yc.api.util.HCodeUtil;
import com.yunqu.yc.api.util.PhoneCryptor;
import com.yunqu.yc.api.util.TaskObjTableUtils;
/**
 * 磐石语音文件同步服务
 * <AUTHOR>
 *
 */

public class PetraCommandService extends IService {
	

	protected static EasyQuery readQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.READ_DS_NAME);
	
	private   int  loadEntInfoCount = 0;
	
	private   int CACHE_TIME = 120;

	private EasyCache cache = CacheManager.getMemcache();
	

		
	/**
	 * BaseServlet默认实现类service,处理请求
	 */
	public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
		
		
		
		
		//请求命令
		String command = StringUtils.trimToEmpty(jsonObject.getString("command"));
		
		String dataString = StringUtils.trimToEmpty(jsonObject.getString("data"));
		if(StringUtils.isBlank(dataString)){
			dataString = "{}";
		}
		/**
		 * {
			"clientId": "ocs_cloud",
			"data": "{\"entId\":\"1001\",\"taskId\":\"83767098295447138756331\",\"rowCount\":\"200\"}",
			"serialId": "0D020BCD612941D300020BCD6131A796",
			"sign": "",
			"version": "1",
			"command": "taskGetData",
			"timestamp": "2021-09-03 12:41:58.684",
			"via": "http://172.16.82.6:9059/yc-api/interface"
		}
		 */
		PetraLogger.getLogger().info("["+command + "] << " + jsonObject);
		
		RequestDataModel requestDataModel = new RequestDataModel();
		requestDataModel.setCommand(command);
		requestDataModel.setData(dataString);
		
		String clientId = StringUtils.trimToEmpty(jsonObject.getString("clientId"));
		
		JSONObject dataObject =  null;
		//JSON格式错误
		try{
			dataObject = JSONObject.parseObject(dataString);
		}catch(Exception ex){
			PetraLogger.getLogger().error(ex,ex);
			throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
		}
		
		if(dataObject == null) dataObject = new JSONObject();
		
		String entId = dataObject.getString("entId");
		
		ResponseDataModel  responseDataModel = new ResponseDataModel();
		responseDataModel.setSystemCode(Constants.SYSTEM_CODE_200);
		responseDataModel.setSystemMessage(Constants.SYSTEM_CODE_200_MSG);
		
		//iccs技能组监控
		if  (command.equalsIgnoreCase("iccsMonitorEnt")) {  
			
			try {
				
				JSONArray  skillGroups =  dataObject.getJSONArray("skillgroupMonitors");
				this.skillGroupMonitor(skillGroups);
				JSONArray  ents =  dataObject.getJSONArray("entMonitors");
				this.entMonitor(ents);
				return null;
			} catch (Exception ex) {
				PetraLogger.getLogger().error(Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage(),ex);
				throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
			}
			
		}
		
		/**
		 * Ipmg参数定义
			参数名	参数类型	是否必填	参数说明
			ipmgName	String	必填	Ipmg应用名称
			ipmgStationNo	String	必填	ipmg站点号
			ipmgIpAddress	String	必填	ipmg的ip和端口，格式ip:port
			
		  SbcInfo参数定义
			参数名	参数类型	是否必填	参数说明
			sbcIpAddress	String	必填	Sbc的ip和端口，格式ip:port
			SipPhones	SipPhone[]	必填	ipmg站点号


		 */
		if  (command.equalsIgnoreCase("sipPhoneMonitorNotify")) { 
			
			try {
				if(ServerContext.isDebug()) MonitorLogger.getLogger().info("sipPhoneMonitorNotify->"+dataObject);
				JSONObject   ipmgInfo = dataObject.getJSONObject("ipmgInfo");
				JSONArray  sipPhones =  dataObject.getJSONArray("sipPhones");
				this.sbcMonitor(ipmgInfo, sipPhones);
				return null;
			} catch (Exception ex) {
				PetraLogger.getLogger().error(Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage(),ex);
				throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
			}
			
		}
		
		
		
		 if  (command.equalsIgnoreCase("taskResultNotify")) {  ////任务执行结果通知
				JSONArray  taskResults =  dataObject.getJSONArray("taskResults");
				if(taskResults==null){
					ApiLogger.getLogger().info(Constants.GATE_SERVER+ "taskResultNotify执行失败，原因：参数taskResults为空!");
				
				}
				this.taskResultNotify(taskResults);
				return null;
		 }
		 
		
		// 处理任务通知接口，ocs30秒通知一次
		if (command.equalsIgnoreCase("taskMonitor")) {

			try {
				this.taskMonitor(clientId,dataObject);
			} catch (Exception ex) {
				DBLogger.error(entId,"OCS-taskMonitor", "OCS-taskMonitor", "-", "-",  Constants.GATE_SERVER+command+" -> 处理taskMonitor错误，原因：:"+ex.getMessage());
				TaskLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 处理taskMonitor错误，原因：:"+ex.getMessage(),ex);
				OCSLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 处理taskMonitor错误，原因：:"+ex.getMessage(),ex);
			}
			return null;
		}
		
		String sql = "select count(1) as tcount from CC_ENT where ENT_STATE = 0 and ENT_ID = ?";
		
		
		if(StringUtils.isNotBlank(entId)){
			try {
				if(!QueryFactory.getWriteQuery(entId).queryForExist(sql, new Object[]{entId})){
					responseDataModel.setSystemCode(Constants.SYSTEM_CODE_516);
					responseDataModel.setSystemMessage(Constants.SYSTEM_CODE_516_MSG);
					if(ServerContext.isDebug()) ApiLogger.getLogger().info(Constants.GATE_SERVER+command+" - >> Content:"+requestDataModel.toString());
					if(ServerContext.isDebug()) ApiLogger.getLogger().info(Constants.GATE_SERVER+command+" -> 处理失败，原因：企业ID["+entId+"]状态不正常或不存在！");
					responseDataModel.setData(new JSONObject());
					return JSONObject.parseObject(responseDataModel.toString());
					
				}
			} catch (Exception ex) {
				PetraLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 查询企业信息失败，原因：:"+ex.getMessage(),ex);
				throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
			}
		}else{
			if (command.equalsIgnoreCase("loadSipPhones")) {  //加载企业话机信息

				try {
					PetraLogger.getLogger().info("loadSipPhones->加载企业["+entId+"]sip话机");
					responseDataModel.setData(this.getSipphoneInfo(entId));
					return JSONObject.parseObject(responseDataModel.toString());
				} catch (Exception ex) {
					PetraLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 加载企业["+entId+"]sip话机失败，原因：:"+ex.getMessage(),ex);
					DBLogger.info(entId,"IPMG-sipphone", "IPMG-sipphone", entId, "loadSipPhones,加载企业话机信息失败","加载企业["+entId+"]sip话机失败，原因：:"+ex.getMessage());
					throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
				}
			}
			throw new ServiceException(Constants.SYSTEM_CODE_511,Constants.SYSTEM_CODE_511_MSG);
		}
		
		
		//****以下这部分属于云呼平台提供的接口实现
		
		if (command.equalsIgnoreCase("loadEntInfo")) { //获取企业信息
			
			try {
				loadEntInfoCount++;
				if(ServerContext.isDebug()) DBLogger.info(entId,"ICCS-loadEntInfo", "ICCS-loadEntInfo", "-", "加载企业信息,期间执行次数:"+this.loadEntInfoCount,  "-");
				
				//新增，企业状态不同步为2的企业信息
				if(!"2".equals(EntContext.getContext(entId).getEntType())){
					responseDataModel.setData(this.getEntConfigInfo(entId));
				}
				
			} catch (Exception ex) {
				DBLogger.error(entId,"ICCS-loadEntInfo", "ICCS-loadEntInfo", "-", "加载企业信息", "获取企业信息失败，原因：:"+ex.getMessage());
				PetraLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 获取企业信息失败，原因：:"+ex.getMessage(),ex);
				throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
			}
		}else  if (command.equalsIgnoreCase("taskGetData")) {  //获取任务数据
			
			String taskId = dataObject.getString("taskId");
			//ocs 第一次获取任务数据，mars 针对已经执行从的数据进行重置处理。 fix by tzc ,20230129
			String reset = dataObject.getString("reset");
			
			if("true".equalsIgnoreCase(reset)){
				try {
					String _sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")
								+ " set TASK_STATE = 0   where ENT_ID = ? and  TASK_ID = ? and  TASK_STATE = 1 ";
					QueryFactory.getTaskWriteQuery(entId).executeUpdate(_sql, new Object[] {entId, taskId});
				} catch (Exception ex) {
					TaskLogger.getLogger().error("Reset["+taskId+"] taskObj status to start[0] fail,cuase:"+ex.getMessage(),ex);
				}
			}
			
			String clientIdName = "";
			if(StringUtils.isNotBlank(clientId)) clientIdName = ":"+clientId;
			JSONObject taskInfo = null;
			
			try {
				taskInfo =  this.getTaskinfo(entId, taskId);
			} catch (Exception ex) {
				TaskLogger.getLogger().error("<taskGetData"+clientIdName+"> 获取任务信息异常,原因:"+ex.getMessage(),ex );
			}
			if(taskInfo == null) taskInfo = new JSONObject();
			
			TaskLogger.getLogger().info("<taskGetData"+clientIdName+"> ["+taskInfo.getString("TASK_NAME")+"]<< "+jsonObject);
			int rowCount = dataObject.getIntValue("rowCount");
			if(rowCount <=0) rowCount = 50;
			if(rowCount>500) rowCount = 500;
			int taskBatchCount = Constants.getTaskBatchCount();
			if(taskBatchCount!=200){
				//fix by cly 20240805 如果任务ocs获取批次不等于默认值，说明批次数据被改变
				rowCount = taskBatchCount;
			}
			try {
				long _taskTimer = System.currentTimeMillis();
				JSONObject taskData = this.getTaskData(entId,clientId,taskId,rowCount);
				_taskTimer = System.currentTimeMillis()-_taskTimer;
				try {
					if(_taskTimer>10000){
						MarsAlarm.taskGetData10sAlarm(taskInfo.getString("TASK_NAME"), _taskTimer/1000, dataObject.toJSONString());
					}else{
						if(_taskTimer>3000){
							MarsAlarm.taskGetData3sAlarm(taskInfo.getString("TASK_NAME"), _taskTimer/1000, dataObject.toJSONString());
						}
					}
					CacheUtil.hset("#ocs", "lastGetDataTime", EasyCalendar.newInstance().getDateTime("-"));
					CacheUtil.hset("#ocs", "lastGetDataTaskName", taskInfo.getString("TASK_NAME"));
				} catch (Exception ex1) {
					TaskLogger.getLogger().error(ex1,ex1);
				}
				OCSLogger.getLogger().info("<taskGetData"+clientIdName+"> ["+taskId+","+taskInfo.getString("TASK_NAME")+"][time:"+_taskTimer+"ms] >> "+ taskData);
				TaskLogger.getLogger().info("<taskGetData"+clientIdName+"> ["+taskId+","+taskInfo.getString("TASK_NAME")+"][time:"+_taskTimer+"ms] >> "+ taskData);
				responseDataModel.setData(taskData);
			} catch (Exception ex) {
				try {
					MarsAlarm.taskGetDataErrorAlarm(taskInfo.getString("TASK_NAME"), dataObject.toJSONString()+","+JSONObject.toJSONString(ex));
				} catch (Exception ex1) {
					TaskLogger.getLogger().error(ex1,ex1);
				}
				TaskLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 获取企业["+entId+"]任务["+taskId+"]外呼数据失败，原因：:"+ex.getMessage(),ex);
				DBLogger.error(entId,"OCS-taskGetData", "OCS-taskGetData", taskId , "获取任务数据", "获取企业["+entId+"]任务["+taskId+"]外呼数据失败，原因：:"+ex.getMessage());
				throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
			}
			
		}else if (command.equalsIgnoreCase("taskReset")) {   //重置任务
			
			String taskId = dataObject.getString("taskId");
			try {
				this.resetTask(entId, taskId);
				DBLogger.info(entId,"OCS-taskReset", "OCS-taskReset", taskId, "营销业务数据复位,OCS复位时执行",  "-");
			} catch (Exception ex) {
				PetraLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 重置企业["+entId+"]任务["+taskId+"]任务失败，原因：:"+ex.getMessage(),ex);
				DBLogger.error(entId,"OCS-taskReset", "OCS-taskReset", taskId , "任务重置", "重置企业["+entId+"]任务["+taskId+"]任务失败，原因：:"+ex.getMessage());
				throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
			}
			
		}else if  (command.equalsIgnoreCase("taskList")) {  ////查询企业的任务列表
			
			try {
				responseDataModel.setData(this.getTaskList(entId));
				DBLogger.info(entId,"OCS-taskList", "OCS-taskList", entId, "营销任务获取,OCS重启时执行",  "-");
			} catch (Exception ex) {
				PetraLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 获取["+entId+"]企业执行执行任务失败，原因：:"+ex.getMessage(),ex);
				DBLogger.error(entId,"OCS-taskList", "OCS-taskList", entId , "-", "获取["+entId+"]企业执行执行任务，原因：:"+ex.getMessage());
				throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
			}
		}if (command.equalsIgnoreCase("loadSipPhones")) {  //加载企业话机信息

			try {
				PetraLogger.getLogger().info("loadSipPhones->加载企业["+entId+"]sip话机");
				responseDataModel.setData(this.getSipphoneInfo(entId));
				PetraLogger.getLogger().info("loadSipPhones->加载企业["+entId+"]sip话机完成");
			} catch (Exception ex) {
				PetraLogger.getLogger().error(Constants.GATE_SERVER+command+" -> 加载企业["+entId+"]sip话机失败，原因：:"+ex.getMessage(),ex);
				DBLogger.info(entId,"IPMG-sipphone", "IPMG-sipphone", entId, "loadSipPhones,加载企业话机信息失败","加载企业["+entId+"]sip话机失败，原因：:"+ex.getMessage());
				throw new ServiceException(500,Constants.GATE_SERVER+command+" ->error,cause:"+ex.getMessage());
			}
		}else if  (command.equalsIgnoreCase("callMonitor")) {  //磐石平台呼叫状态通知接口
			dataObject.put("updateTime", System.currentTimeMillis());
		    CacheManager.getMemcache().put("PETRA_CALL_MONITOR", dataObject);
		}
		
		
		PetraLogger.getLogger().info(responseDataModel.getMessage()+ " >> "+responseDataModel.toString());
		
		return JSONObject.parseObject(responseDataModel.toString());
		/**
		   最终生成的格式：
		   {
			 "systemCode":200,
			 "systemMessage":"成功",
			 "stateCode":1,
			 "message":"OK",
			"data":{JSON串} 
			}

		 */
		
	}
	
	

	
	
	
	/**
	 * 任务状态更新通知
	 * @param entId
	 * @param tasks
	 * callResult:
	 * enumItemResult_Success = 0,
	enumItemResult_NoAnswer = 1,
	enumItemResult_UserBusy = 2,
	enumItemResult_UserRelease = 3,
	enumItemResult_NetworkBusy = 4,
	enumItemResult_UnallocateNumber = 5,
	enumItemResult_UserReject = 6,
	enumItemResult_UserPowerOff = 7,
	enumItemResult_SystemFailed = 99,
	enumItemResult_OtherFailed = 100
	
	{"taskResults":[{"serialId":"89a7395eb9c94652808bc8d75fd66140","entId":"1","taskId":"74e4b3b405d34aa9bf8993fd4c8da5d0",
	"callResult":"0","callResultDesc":"Success","failResonCode":"","failResonDesc":"",
	"userData":{"taskCallType":"auto","callSerialId":"efa9f968ef094bc5bb52b7a260a99309","serialId":"89a7395eb9c94652808bc8d75fd66140",
	"taskId":"74e4b3b405d34aa9bf8993fd4c8da5d0","endId":"1","welcomeFile":"/flow/vox/",
	"queueFile":"","ocsSkillGroup":"1","ocsSessionId":"13518","ocsCallbackUrl":"http://192.168.11.163:7601/ocs_cloud/ocs"}}]}) 

	 */
	private void taskResultNotify(JSONArray tasks){
		if(tasks == null){
			TaskLogger.getLogger().info("tasks is null!");
		}
		for(int i = 0 ;i<tasks.size();i++){
			JSONObject  task = tasks.getJSONObject(i);
			//MQClient.sendMesage("taskResultNotify",task.toJSONString());
//			TaskResultHandler.addNotify(task);
			EventDispatcher.addEvent(new TaskResultExecutor(task));
		}
	}
	
	
	
	/**
	 * 重置任务状态，只有为状态5，启动中的任务才需要重置。 
	 * @param entId
	 * @param taskId
	 * @throws SQLException 
	 */
	private void resetTask(String entId,String taskId) throws Exception{
		  String sql =  "select TASK_STATE from "+EntContext.getContext(entId).getTableName( "CC_TASK")+" where ENT_ID = ? and  TASK_ID = ? ";
		  
		  int val = readQuery.queryForInt(sql, new Object[]{entId,taskId});
		  String status = "1";  
		  if(val ==5 ) status = "3";  //5代表启动中，其它状态代表停止
		  
		IService service = ServiceContext.getService("YC-TASK-NOTIFY-SERVICE");
		JSONObject jsonIn = new JSONObject();
		jsonIn.put("entId", entId);
		jsonIn.put("taskId", taskId);
		jsonIn.put("status", status);
		service.invoke(jsonIn);
	}
	
	
	private  JSONObject getTaskPhoneOrder(String entId,String taskId){
		String sql = "select *  from "+EntContext.getContext(entId).getTableName("CC_TASK")+" where TASK_ID = ? ";
		JSONObject phoneOrder = new JSONObject();
		phoneOrder.put("TEL_NUM1", "1");
		phoneOrder.put("TEL_NUM2", "2");
		phoneOrder.put("TEL_NUM3", "3");
		phoneOrder.put("TEL_NUM4", "4");
		phoneOrder.put("TEL_NUM5", "5");
		phoneOrder.put("TEL_NUM6", "6");
		try {
			EasyRow row =  QueryFactory.getTaskWriteQuery(entId).queryForRow(sql, new Object[]{taskId}) ;
			String extConfString = row.getColumnValue("EXT_CONF");
			ApiLogger.getLogger().info("extConfString->"+extConfString);
			if(StringUtils.isNotBlank(extConfString)){
				JSONObject extConfObject = JSONObject.parseObject(extConfString);
				if(!extConfObject.containsKey("telSort")) return phoneOrder;
				return extConfObject.getJSONObject("telSort");
			}
		} catch (Exception ex) {
			ApiLogger.getLogger().error("getTaskPhoneOrder() error,cause:"+ex.getMessage(),ex);
		}
		return phoneOrder;
	}
	
	
	/**
	 * 检查呼叫频次
	 * @param caller  主叫
	 * @param maxHourCall 每小时最大外呼量
	 * @param maxDayCall  每天最大外呼量
	 * @return int  0  正常  1 超每小时最大外呼次数  2 超每天最大外呼次数
	 */
	public boolean checkCallerLimit(String caller,int maxHourCall,int maxDayCall){
		
		if(maxHourCall<=0 && maxDayCall<=0) return true ;  //如果不限制，这不做处理。
		
		EasyCalendar cal = EasyCalendar.newInstance();
		int day = cal.getDateInt();
		int hour = cal.getCalendar().get(EasyCalendar.HOUR);
		String dayKey = "marscall_"+day+"_"+caller;
		String hourKey = "marscall_"+hour+"_"+caller;
		
		if(maxHourCall>0){
			String hourCallCount = CacheUtil.get(hourKey);
			if(StringUtils.isNotBlank(hourCallCount)){
				if(Integer.parseInt(hourCallCount)>=maxHourCall){
					TaskLogger.getLogger().info("checkCallerLimit("+caller+")已超过外呼限制  ->每小时最大外呼次数:"+maxHourCall+",本小时外呼次数："+hourCallCount);
					return false;
				}
			}
		}
		
		if(maxDayCall>0){
			String dayCallCount = CacheUtil.get(dayKey);
			if(StringUtils.isNotBlank(dayCallCount)){
				if(Integer.parseInt(dayCallCount)>=maxDayCall){
					TaskLogger.getLogger().info("checkCallerLimit("+caller+") 已超过外呼限制 ->每日最大外呼次数:"+maxDayCall+",当日已经外呼次数："+dayCallCount);
					return false;
				}
			}
		}
		return true;
	}
	
	/**
	 * 获得当前呼叫任务的来显电话列表
	 * @param taskKey
	 * @param entId
	 * @param taskId
	 * @return
	 */
	private Map<String,Map<String,String>> getTaskPhones(String cacheKey, String entId,String taskId){
		
		Map<String,Map<String,String>>  phones = new HashMap<String,Map<String,String>>();
//		try {
//			phones =  CacheManager.getMemcache().get(cacheKey);
//		} catch (Exception ex) {
//			CacheManager.getMemcache().delete(cacheKey);
//		}
		
		EntContext  entContext = EntContext.getContext(entId);
		
		if(phones.size()==0){  //避免没有配置的情况。
		 
		  //查询在任务中单独配置一个外显号码的情况
		  String sql = "select t2.PREFIX_NUM,t2.CALL_PREFIX_CODE,t2.AREA_CODE from "+EntContext.getContext(entId).getTableName("CC_TASK")+" t1,CC_PREFIX t2  where t1.ENT_ID = t2.ENT_ID and t1.PREFIX_NUM = t2.PREFIX_NUM  and t2.PREFIX_STATE = 0 and t1.TASK_ID = ?";
		  try {
			EasyRow row =  QueryFactory.getTaskWriteQuery(entId).queryForRow(sql, new Object[]{taskId}) ;
			if(row != null){
				//如果任务存在外显号码
				if(StringUtils.isNotBlank(row.getColumnValue("PREFIX_NUM"))){
					String areacode =  row.getColumnValue("AREA_CODE");
					if(StringUtils.isNotBlank(areacode)){  //如果外显号码配置了区号
						Map<String,String> areaMap = phones.get(areacode);
						if(areaMap == null) {
							areaMap = new HashMap<String,String>();
						}
						areaMap.put(row.getColumnValue("PREFIX_NUM"),row.getColumnValue("CALL_PREFIX_CODE"));
						//保存的确对应的外显号码列表
						phones.put(areacode, areaMap);
					}
					
					//areacode0 保存了所有本任务的号码数据信息。
					String areacode0 =  "0"; 
					Map<String,String> _areaMap0 = phones.get(areacode0);
					if(_areaMap0 == null) {
						_areaMap0 = new HashMap<String,String>();
					}
					_areaMap0.put(row.getColumnValue("PREFIX_NUM"),row.getColumnValue("CALL_PREFIX_CODE"));
					phones.put(areacode0, _areaMap0);
					
				}
			}
			
			sql = "select t3.PREFIX_NUM,t3.CALL_PREFIX_CODE,t3.AREA_CODE from "+EntContext.getContext(entId).getTableName( "CC_TASK")+" t1 , "
					+ " CC_PREFIX_GROUP_PREFIX t2 ,CC_PREFIX t3  where t1.PREFIX_GROUP_ID = t2.PREFIX_GROUP_ID and "
					+ " t1.ENT_ID = t3.ENT_ID and t2.PREFIX_NUM = t3.PREFIX_NUM and t3.PREFIX_STATE = 0   and   TASK_ID = ?";
			List<EasyRow> rows = QueryFactory.getTaskWriteQuery(entId).queryForList(sql, new Object[]{taskId}) ;
			for(EasyRow _row:rows){

				if(StringUtils.isBlank(_row.getColumnValue("PREFIX_NUM"))) continue;
				// fix by tzc 20230302  增加对外显号码的呼叫限制的判断。
				if(!checkCallerLimit(_row.getColumnValue("PREFIX_NUM"),entContext.getMaxCallerHourCallCount(),entContext.getMaxCallerDayCallCount())){
					continue;
				}
				
				String areacode0 = "0"; 
				Map<String,String> _areaMap0 = phones.get(areacode0);
				if(_areaMap0 == null) {
					_areaMap0 = new HashMap<String,String>();
				}
				_areaMap0.put(_row.getColumnValue("PREFIX_NUM"),_row.getColumnValue("CALL_PREFIX_CODE"));
				phones.put(areacode0, _areaMap0);

				String areacode =  _row.getColumnValue("AREA_CODE");
				if(StringUtils.isNotBlank(areacode)){
					Map<String,String> _areaMap = phones.get(areacode);
					if(_areaMap == null) {
						_areaMap = new HashMap<String,String>();
					}
					_areaMap.put(_row.getColumnValue("PREFIX_NUM"),_row.getColumnValue("CALL_PREFIX_CODE"));
					phones.put(areacode, _areaMap);
				}
			}
			//CacheManager.getMemcache().put(cacheKey, phones, CACHE_TIME);  //缓存时间为300秒,启动任务的时候需要清理cache
		  } catch (Exception ex) {
			  TaskLogger.getLogger().error(
						"getNextCalled error  -> taskId:" + taskId + ",entId:" + entId + ",cause:" + ex.getMessage(), ex);
		  }
		}
		return phones;
		
	}
	
	
	/**
	 * 获得企业的配置信息
	 * @param entId 企业ID
	 * @return
	 */
	private JSONObject getEntRes(String entId){
		JSONObject entObj = (JSONObject)CacheManager.getMemcache().get("ENT_RES_"+entId);
		if(entObj==null){
			String sql = "select * from CC_ENT_RES   where ENT_ID = ? ";
			try {
				entObj = QueryFactory.getWriteQuery(entId).queryForRow(sql, new Object[]{entId}, new JSONMapperImpl());
			} catch (Exception ex) {
				 ApiLogger.getLogger().error(
							"getEntConfig error  -> entId:" + entId + ",cause:" + ex.getMessage(), ex);
			}
			CacheManager.getMemcache().put("ENT_RES_"+entId,entObj);
		}
		return entObj;
		
	}
	
	/**
	 * fix cly:200条数据这里检查用了13秒，减少数据库查询时间
	 * 检查号码是否已经超过指定的呼叫次数
	 * @return
	 */
	private int checkPhone(String taskId,String objId,EntContext entContext,JSONObject entRes,String called){
		CheckPhoneService service = CheckPhoneService.getService();
		return service.checkPhone(taskId, objId, entContext, entRes, called);
	}
	
	
	private String getRandmPhone(String[]  phones ){
		Random r = new Random();
		return phones[r.nextInt(phones.length)];
	}
	
	
	/**
	 * 加载企业的话机信息
	 * @param entId
	 * @return  PHONE_TYPE :  话机类型，１、硬话机　　２、SIP硬话机　　３、SIP软话机  　4、外线  5、webrtc话机
	 * @throws Exception
	 */
	private JSONObject getSipphoneInfo(String entId) throws Exception {
		
		JSONObject data = new JSONObject();
		
		String sql = "";
		//PetraLogger.getLogger().info("getSipphoneInfo->1");
		if(StringUtils.isBlank(entId)){
			sql = "select t1.ENT_ID,t1.PHONE_NUM,t1.PHONE_PWD,t1.PHONE_TYPE from CC_PHONE   t1  , CC_ENT t2 where  t1.PHONE_TYPE in (2,3,5) and  t1.ENT_ID = t2.ENT_ID  and t2.ENT_TYPE <> 2 and t2.ENT_STATE = 0 ";
		}else{
			sql = "select ENT_ID,PHONE_NUM,PHONE_PWD,PHONE_TYPE from CC_PHONE  where PHONE_TYPE in (2,3,5) and   ENT_ID  = '"+entId+"'";
		}
		//PetraLogger.getLogger().info("getSipphoneInfo->2");
		try {
			EasyQuery query = QueryFactory.getWriteQuery(null);
			//PetraLogger.getLogger().info("getSipphoneInfo->3");
			query.setMaxRow(50000);
			//PetraLogger.getLogger().info("getSipphoneInfo->4,sql="+sql);
			List<EasyRow> rows = query.queryForList(sql,new Object[]{});
			//PetraLogger.getLogger().info("getSipphoneInfo->5");
			JSONArray phones = new JSONArray();
			for(EasyRow row:rows){
				JSONObject item = new JSONObject();
				item.put("account", row.getColumnValue("PHONE_NUM"));
				item.put("password", row.getColumnValue("PHONE_PWD"));
				item.put("entId", row.getColumnValue("ENT_ID"));
				phones.add(item);
			}
			//PetraLogger.getLogger().info("getSipphoneInfo->6");
			data.put("fetchCount", phones.size());
			data.put("sipPhones", phones);
			PetraLogger.getLogger().info("loadSipPhones->加载企业["+entId+"]sip话机成功,data:"+data.toJSONString());
			DBLogger.info(entId,"IPMG-sipphone", "IPMG-sipphone", entId, "loadSipPhones,加载企业话机信息成功","SIP话机数："+phones.size());
		} catch (Exception ex) {
			PetraLogger.getLogger().error("loadSipPhones->加载企业["+entId+"]sip话机失败,原因:"+ex.getMessage(),ex);
		}
		
		return data;
	}
	
	
	/**
	 * 检查任务是否在企业的工作时间配置内
	 * 
	 * 2017-12-29 12:40:48,897 INFO    [undefine:ocs-taskgetdata]     
	 *  isWorktime(entId:1000,taskId:84854826896479995790819)=false,config->
	 *  {"ENT_ID":"1000","BEGIN_TIME_1":"11:30","END_TIME_1":"12:30","BEGIN_TIME_2":"14:00","END_TIME_2":"17:00","BEGIN_TIME_3":"","END_TIME_3":""}  at com.yunqu.yc.api.server.InterfaceServlet.isWorktime(InterfaceServlet.java:816)
	 * 
	 * @param entId
	 * @return
	 */
//	private boolean isWorktime(String entId,String taskId){
//		String sql = "select * "
//				+ "  from "+EntContext.getContext(entId).getTableName( "CC_ENT_WORKTIME")+" t1, CC_BUSI_ORDER t2 "
//				+ "  where  t1.BUSI_ORDER_ID = t2.BUSI_ORDER_ID and  USE_FLAG = 1 and t1.ENT_ID = ?  and t2.BUSI_ID = '002'";
//		try {
//			JSONObject row = QueryFactory.getTaskWriteQuery(entId).queryForRow(sql, new Object[]{entId},new JSONMapperImpl());
//			if(row == null) return true;
//			boolean bl = TimeChecker.isWorktime(row.getString("BEGIN_TIME_1"), row.getString("END_TIME_1")
//					, row.getString("BEGIN_TIME_2"), row.getString("END_TIME_2"),
//					row.getString("BEGIN_TIME_3"), row.getString("END_TIME_3"),row.getString("WORKDAY_CONF"));
////			TaskLogger.getLogger().info("isWorktime(entId:"+entId+",taskId:"+taskId+")="+bl+",work time config->"+row.toJSONString());
//			
//			TaskLogger.getLogger().info("isWorktime(entId:"+entId+",taskId:"+taskId+")="+bl+",config->"+row.toJSONString());
////			if(!bl){
////				TaskLogger.getLogger().info(
////						"getTaskData(entId:"+entId+",taskId:"+taskId+") fail ,cause: not in work time ->"+row.toJSONString());
////			}
//			return bl;
//		} catch (Exception ex) {
//			TaskLogger.getLogger().error(
//					"getTaskData  error  -> entId:" + entId + ",cause:isWorktime() error," + ex.getMessage(), ex);
//		}
//		return true;
//	}
	
	private String getTelNum(EasyRow row,String order ,JSONObject phoneOrder){
		if(order.equals(phoneOrder.getString("TEL_NUM1"))) return row.getColumnValue("TEL_NUM1");
		if(order.equals(phoneOrder.getString("TEL_NUM2"))) return row.getColumnValue("TEL_NUM2");
		if(order.equals(phoneOrder.getString("TEL_NUM3"))) return row.getColumnValue("TEL_NUM3");
		if(order.equals(phoneOrder.getString("TEL_NUM4"))) return row.getColumnValue("TEL_NUM4");
		if(order.equals(phoneOrder.getString("TEL_NUM5"))) return row.getColumnValue("TEL_NUM5");
		if(order.equals(phoneOrder.getString("TEL_NUM6"))) return row.getColumnValue("TEL_NUM6");
		return "";
	}
	
	
	/**
	 * 
	 * @param entId 企业ID
	 * @param clientId  ocs站点号，如：ocs_cloud
	 * @param taskId  要获取数据的任务ID
	 * @param rowCount  本次获取任务的最大返回记录数
	 * @return
	 * @throws Exception
	 */
	private JSONObject getTaskData(String entId,String clientId,String taskId,int rowCount) throws Exception {
	    
	    long timer = System.currentTimeMillis();
	    
		EntContext context = EntContext.getContext(entId);
		
		JSONObject taskInfo = this.getTaskinfo(entId, taskId);
		
		String ocsSkillGroup = taskInfo.getString("SKILL_GROUP_ID");
		
//		int  maxRingTime  = 30;
//		
//		if(taskInfo.containsKey("maxRingTime")){
//			
//		}
		
		
		EasyCalendar cal = EasyCalendar.newInstance();
//		int maxCallCount = this.getEntCallCount(context.getResEntId());
//		//如果企业的本次需要获取的外呼任务数 大于 企业最大的可外呼任务数，则只取最大的可外呼剩余数。
//		if(batchCount > maxCallCount) batchCount = maxCallCount;
		
		boolean isTargetOCS = true;
		String clientIdName = "";
		
//		//如果企业没有指定OCS
//		if(StringUtils.isBlank(context.getOcsName())){
//			//如果没有指定任务外呼的OCS，则指定当前的任务外呼OCS节点
//			if(StringUtils.isBlank(context.getRandomOcsName())){
//				context.setRandomOcsName(clientId);
//			}else{  //如果指定的OCS节点，对节点状态进行判断，看是否已经超时 
//				try {
//					/**
//					 * {
//							"ocs_cloud": "{\"clientId\":\"ocs_cloud\",\"timestamp\":1653279689207,\"updateTime\":\"2022-05-23 12:21:29\",\"url\":\"http://172.16.82.2:7601/ocs_cloud/ocs\"}",
//							"ocs_cloud2": "{\"clientId\":\"ocs_cloud2\",\"timestamp\":1653279689595,\"updateTime\":\"2022-05-23 12:21:29\",\"url\":\"http://172.16.82.3:7601/ocs_cloud2/ocs\"}"
//					   }
//					 */
//					String clientString = CacheUtil.hget("PetraCommands_taskNotify", context.getRandomOcsName());
//					TaskLogger.getLogger().info("Hget:PetraCommands[commond:PetraCommands_taskNotify,clientId:"+context.getRandomOcsName()+"]->"+clientString);
//					if(StringUtils.isNotBlank(clientString)){
//						JSONObject clientObj = JSONObject.parseObject(clientString);
//						if(clientObj!=null){
//							long clientUpdateTime = clientObj.getLongValue("timestamp");
//							//如果当前的clientId已经超时，请移除原来的OCS。
//							if(System.currentTimeMillis() - clientUpdateTime > 300*1000){
//								TaskLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] OCS["+context.getRandomOcsName()+"]节点的心跳更新时间超时，把当前OCS["+clientId+"]节点设置为任务外呼节点。");
//								context.setRandomOcsName(clientId);
//							}
//						}
//					}
//				} catch (Exception ex) {
//					TaskLogger.getLogger().error(ex,ex);
//				}
//			}
//		}
		
		//判断当前任务指定的OCS是否为当前的任务节点。
		if(StringUtils.isNotBlank(context.getTaskOcsName())){
			isTargetOCS =  context.getTaskOcsName().equalsIgnoreCase(clientId);
			clientIdName =  ":"+clientId;
		}
		
		JSONObject data = new JSONObject();  //返回的data对象
		JSONArray tasks = new JSONArray();  //任务数据
		String batchId = RandomKit.randomStr();
		 //任务总数
	    int totalCount = 0;
	    //剩余任务总数
	    int overplusCount = 0;
	    String dateTime = cal.getDateTime("-");
	    boolean isTaskWorktime =  CheckWorkTimeService.isTaskWorkTime(taskInfo.getJSONObject("EXT_CONF"), dateTime,taskId, taskInfo.getString("TASK_NAME"));
	    boolean isEntWorktime  =  CheckWorkTimeService.isWorkTime(entId, dateTime,  taskInfo.getString("BUSI_ORDER_ID"));
	    
	    TaskLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] 检查工作时间 -> 任务工作时间："+isTaskWorktime+",企业工作时间:"+isEntWorktime);
	    
	    boolean isFeeOk = context.feeCheck();
	    
	    TaskLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] 是否预付费:"+context.isPrePayType()+"，计费状态："+(isFeeOk?"正常":"欠费"));
	    
		if(isTaskWorktime && isEntWorktime  && isTargetOCS && isFeeOk){
			
//			//这里防止错误发生
//			if(batchCount<=0) batchCount = 100;
//			if(batchCount>500) batchCount = 500;
			String cacheKey = "PREFIX_NUM_"+taskId;
			
			//获得企业的来显号码
			Map<String,Map<String,String>>  phoneMap = getTaskPhones(cacheKey,entId,taskId);
			
			TaskLogger.getLogger().info("<taskGetData"+clientIdName+"> ["+taskId+","+taskInfo.getString("TASK_NAME")+"] 任务外线号码列表 -> " + JSONObject.toJSONString(phoneMap));
			
			if( phoneMap==null ||phoneMap.size() == 0){
				TaskLogger.getLogger().warn("<taskGetData"+clientIdName+"> ["+taskId+","+taskInfo.getString("TASK_NAME")+"] -> 获取任务数据失败，原因：外显号码为空或超过呼叫管控！");
				//DBLogger.error(entId,"OCS-taskGetData", "OCS-taskGetData",taskId, taskName,"获取任务数据失败，原因：来显号码为空！");
				throw new Exception("Get task data fail,cause: Not config display phonenum!");
			}
			
			
			JSONObject entRes = this.getEntRes(context.getResEntId());
		    long runTime  = System.currentTimeMillis()/1000;
		    
			/**
			 * 如果任务状态为非执行中状态，则不返回任务数据
			 */
			if (taskInfo.getString("TASK_NAME") != null) {
			   
					//String sql = "select OBJ_ID,TASK_ID,BUSI_ORDER_ID,TEL_NUM1,TEL_NUM2,TEL_NUM3,TEL_NUM4 from "
					String sql = "select * from "
							+ TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")
							+ " where TASK_ID = ? and NEXT_RUN_TIME < ?  and TASK_STATE = 0 order by NEXT_RUN_TIME";
					
					if(context.isTaskBatch()){
						//fix cly：查询条件不要企业ID，改变索引 IDX_CC_TASK_OBJ_4
						//ALTER TABLE CC_TASK_OBJ DROP INDEX IDX_CC_TASK_OBJ_4;
						//ALTER TABLE CC_TASK_OBJ ADD INDEX IDX_CC_TASK_OBJ_4 (NEXT_RUN_TIME,TASK_ID,BATCH_ID,TASK_STATE);
						sql = "select t1.* from "
								+ TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")
								+ " t1 , "+EntContext.getContext(entId).getTableName("CC_TASK_BATCH")+"  t2 "
								+ "where  t1.BATCH_ID = t2.BATCH_ID and t1.TASK_ID = t2.TASK_ID and t2.BATCH_STATE = 0  and  t1.TASK_ID = ? and t1.NEXT_RUN_TIME < ?  and t1.TASK_STATE = 0 order by t2.EXECUTE_ORDER , t1.NEXT_RUN_TIME";
					}
					
					List<EasyRow> rows;
					JSONObject phoneOrder = this.getTaskPhoneOrder(entId,taskId);
					
					//ApiLogger.getLogger().info("phoneOrder->"+phoneOrder);
					try {
						rows = QueryFactory.getTaskWriteQuery(entId).queryForList(sql, new Object[] { taskId,runTime},1,rowCount);
						
						TaskLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] -> 查询返回记录数："+rows.size());
						OCSLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] -> 查询返回记录数："+rows.size());
						for (int i = 0 ;i<rows.size();i++) {
							//fix by tzc 20200102 ,增加任务获取时间的超时判断，如果任务处理时间超过20S的，直接返回当前已经处理过的任务数据。
							if(System.currentTimeMillis() - timer > 10*1000) {
								TaskLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] -> 返回记录数:"+rows.size()+",耗时:"+(System.currentTimeMillis() - timer));
								break;
							}
							EasyRow row  =  rows.get(i);
							JSONObject task = new JSONObject();
							String objId = row.getColumnValue("OBJ_ID");
							task.put("serialId", objId);
							
							String tel1 = PhoneCryptor.getInstance().decrypt(entId, getTelNum(row,"1",phoneOrder));
							String tel2 = PhoneCryptor.getInstance().decrypt(entId, getTelNum(row,"2",phoneOrder));
							String tel3 = PhoneCryptor.getInstance().decrypt(entId, getTelNum(row,"3",phoneOrder));
							String tel4 = PhoneCryptor.getInstance().decrypt(entId, getTelNum(row,"4",phoneOrder));
							String tel5 = PhoneCryptor.getInstance().decrypt(entId, getTelNum(row,"5",phoneOrder));
							String tel6 = PhoneCryptor.getInstance().decrypt(entId, getTelNum(row,"6",phoneOrder));
							
					    	task.put("tel1",  tel1);
							task.put("tel2",  tel2);
							task.put("tel3",  tel3);
							task.put("tel4",  tel4);
							task.put("tel5",  tel5);
							task.put("tel6",  tel6);
							
							//如果外呼号码都为空的时候，证明这个号码外呼受限，更新CALL_RESULT为403,呼叫受限。
							if (StringUtils.isBlank(tel1) && StringUtils.isBlank(tel2) && StringUtils.isBlank(tel3)
									&& StringUtils.isBlank(tel4) && StringUtils.isBlank(tel5)
									&& StringUtils.isBlank(tel6)) {
								tel1 = this.getRealCalled(row.toJSONObject());
								if (tel1 == null) {
									try {
										sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")
												+ " set TASK_STATE = 9  , SUBMIT_TIME = ? ,CALL_RESULT = 403 where ENT_ID = ? and  TASK_ID = ?  and  OBJ_ID  = ? ";
										QueryFactory.getTaskWriteQuery(entId).executeUpdate(sql, new Object[] {
												cal.getDateTime("-"), entId, taskId, objId });
									} catch (Exception ex) {
										TaskLogger.getLogger().error("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] error,cause:" + ex.getMessage(), ex);
									}
									TaskLogger.getLogger().warn(
											"<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] 处理任务数据["+objId+"]失败，原因：TEL_NUM1~TEL_NUM4 号码为空.");
									continue;
								}
							}
							
							task.put("taskId", row.getColumnValue("TASK_ID"));
						    
						    //保存当前呼叫顺序，缺少为0，则这个记录没有进行外呼过， 如果为2，则第二个号码已经被外呼。
						    int curCallIndex = 0;  //呼叫序列
						    if(StringUtils.isNotBlank(row.getColumnValue("CALL_IDX"))){
						    	curCallIndex = Integer.parseInt(row.getColumnValue("CALL_IDX"));
						    }
						    
						    //如果 curCallIndex == 1 
						   
						    //当前obj包含多少个手机号码。
						    int maxCallIndex = 0;
						   
						    //保存obj最大的外呼号码序号。
						    for(int idx = 1 ;idx <=6;idx++){// 这里判断，如果已经外呼过的额号码，就不能在外呼
						    	if(StringUtils.isNotBlank(task.getString("tel"+idx))){
						    		maxCallIndex = idx;
						    	}
						    }
						    
						    //改成每次只送一个号码给OCS，统一放在tel1字段。
						    String _tel = "";
						    int callResult = 0 ;
						 // 这里判断，如果已经外呼过的额号码，就不能在外呼,从1开始遍历6个号码，如果获取号码等于当前外呼标志的号码，则作为当次任务所外呼的号码
						    for(int idx = 1 ;idx <=6;idx++){
						    	if(idx>curCallIndex){
						    		if(StringUtils.isNotBlank(task.getString("tel"+idx))){
						    			curCallIndex = idx;
						    			_tel = task.getString("tel"+curCallIndex);
						    			if(StringUtils.isBlank(_tel)) continue;
						    			//检查当前号码的状态，包括：黑名单，可外呼区域等。  4031 超次呼叫    4032 超频呼叫  4033  企业黑名单   4034 外呼范围限制   4035 任务黑名单  5001  红名单
						    			callResult = this.checkPhone(taskId,objId,context,entRes, _tel);
						    			if(callResult>0) _tel = "";
						    			break;
							    	}
						    	}
						    }
						    
//							if(ServerContext.isDebug()){
//								TaskLogger.getLogger().info("[DEBUG] TaskObj["+row.getColumnValue("OBJ_ID")+"] curCallIndex->"+curCallIndex +",maxCallIndex="+maxCallIndex +",_tel"+_tel);
//							}
							
							//如果找不到可外呼的号码，则把任务状态更新成已完成。
							if(StringUtils.isBlank(_tel)){
								try {
									
									EasyRecord record = new EasyRecord(TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ"), "OBJ_ID");
									record.setPrimaryValues(objId);
									record.put("TASK_STATE", 9);
									if(callResult>0){
										record.put("CALL_RESULT", callResult);
									}
									record.put("SUBMIT_TIME",cal.getDateTime("-"));
									QueryFactory.getTaskWriteQuery(entId).update(record);
									
								} catch (Exception ex) {
									TaskLogger.getLogger().error("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] error,cause:"+ ex.getMessage(),ex);
								}
								TaskLogger.getLogger().warn(
										"<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] 处理任务数据["+row.getColumnValue("OBJ_ID")+"]失败，原因：TEL_NUM1~TEL_NUM4 号码为空.");
								continue;
							}
						    
							//获得号码所在的地区信息
						    JSONObject areaInfo  = HCodeUtil.getAreacode(_tel); 
							String areacode = "0";
							if(areaInfo!=null) areacode = areaInfo.getString("area");
							if(StringUtils.isBlank(areacode)) areacode = "0";
							
							long  _serviceTimer = System.currentTimeMillis();
							
							//获得真实的外呼号码,需要根据业务场景来获取外部服务来获取真实的被叫，因为根据不同平台的要求，根据送出去的地区，来设定被叫的扦插吗。
							String caller = this.getRealCaller(entId,taskId,"",_tel,areacode,objId);
							_serviceTimer = System.currentTimeMillis()- _serviceTimer;
							if(_serviceTimer > 100l){
								TaskLogger.getLogger().warn("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"]-> getPhoneByService(TASK_GET_CALLER_SERVICE) tel["+_tel+"] invoke time "+_serviceTimer+"ms");
							}
							
							//如果caller的返回值为空时，采用原有的处理方式来进行处理。
							if(StringUtils.isBlank(caller)){
								Map<String,String> _callerMap = phoneMap.get(areacode);
								if(_callerMap == null)  _callerMap = phoneMap.get("0");
								
								if(_callerMap == null  || StringUtils.isBlank(_tel)){
									try {
										sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")
												+ " set TASK_STATE = 9  , SUBMIT_TIME = ? ,CALL_RESULT = 403 where ENT_ID = ? and  TASK_ID = ?  and  OBJ_ID  = ? ";
										QueryFactory.getTaskWriteQuery(entId).executeUpdate(sql, new Object[] { cal.getDateTime("-"), entId, taskId, row.getColumnValue("OBJ_ID") });
									} catch (Exception ex) {
										TaskLogger.getLogger().error("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] error,cause:"+ ex.getMessage(),ex);
									}
									TaskLogger.getLogger().error("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] taskGetData error,cause:telNum["+_tel+"] not found dispalyCaller.");
									continue;
								}
								String[] phones = new String[_callerMap.size()];
								_callerMap.keySet().toArray(phones);
								caller = this.getRandmPhone(phones);
							}
						
							String realCalled = _tel;
							
							_tel = HCodeUtil.getReallyCalled(entId, caller, _tel);
							//task.put("tel1",  HCodeUtil.addCallPrefix(_callerMap.get(phone),tel1));
//							if(ServerContext.isDebug()){
//								TaskLogger.getLogger().info("[DEBUG] TaskObj["+row.getColumnValue("OBJ_ID")+"] telOrder->"+curCallIndex +",maxCallIndex="+maxCallIndex +",_tel"+_tel);
//							}
							
							
							task.put("tel1", _tel);
							task.put("tel2", "");
							task.put("tel3", "");
							task.put("tel4", "");
							task.put("tel5", "");
							task.put("tel6", "");
							
							task.put("displayCaller",caller);  //这个需要修改							
							//fix by tzc : 20210622 ， 增加origCalled 需要在企业配置中手工配置。
							String origCalled =  this.getOrigCalled(entId, caller, _tel, StringUtils.trimToEmpty(taskInfo.getString("ROBOT_MODEL_ID")),8);
							if(StringUtils.isBlank(origCalled)){
								EntContext entContext = EntContext.getContext(entId);
						        origCalled = entContext.getExtConf("origCalled");
							}
							
							
							task.put("origCalled",origCalled);
							
//							if(ServerContext.isDebug()){
//								TaskLogger.getLogger().info("[DEBUG] TaskObj["+row.getColumnValue("OBJ_ID")+"] call taskObj ->"+task);
//							}
							
							JSONObject userData = new JSONObject();
							userData.put("objId", objId);
							userData.put("busiOrderId", row.getColumnValue("BUSI_ORDER_ID"));
							userData.put("callSerialId", RandomKit.randomStr());
							userData.put("taskId", row.getColumnValue("TASK_ID"));
							userData.put("batchId", batchId);
							userData.put("curCallIndex",curCallIndex);
							userData.put("maxCallIndex", maxCallIndex);
							userData.put("realCalled", realCalled);
							userData.put("displayCaller", caller);
							userData.put("custPhone", realCalled);
							userData.put("ocsSkillGroup", ocsSkillGroup);
							
							//20201217 新版本OCS机器人自动外呼-获取任务号码，需要从userData中获取welcomeFile，queueFile,flowName，robotId
							userData.put("welcomeFile", StringUtils.trimToEmpty(taskInfo.getString("VOX_PATH")));
							userData.put("queueFile", StringUtils.trimToEmpty(taskInfo.getString("QUEUE_VOX_PATH")));
							userData.put("flowName", StringUtils.trimToEmpty(taskInfo.getString("ROBOT_FLOWNAME")));// 设定机器人外呼流程名
							userData.put("robotId", StringUtils.trimToEmpty(taskInfo.getString("ROBOT_MODEL_ID"))); // 设定机器人ID流程ID
							userData.put("callbackUrl", EntContext.getContext(entId).getCallbackUrl());
							userData.put("entId", entId);
							//外呼最大振铃响应时间
							userData.put("maxRingTime", 30);
//							userData.put("recordFlag", EntContext.getContext(entId).getExtConf("RECORD_FLAG", "0"));////任务是否启动录音
//							userData.put("ringCheckFlag", EntContext.getContext(entId).getExtConf("RING_CHECK_FLAG", "0"));////是否检测回铃音
//							userData.put("autoOutboundLicense", StringUtils.trimToEmpty(taskInfo.getString("AUTO_CALL_LICENSE")));

							//音色
							userData.put("robotTimbre", StringUtils.trimToEmpty(taskInfo.getJSONObject("EXT_CONF").getString("robotTimbre")));
							//转坐席
							if(StringUtils.isNotBlank(taskInfo.getJSONObject("EXT_CONF").getString("agentId"))){
								userData.put("agentId", StringUtils.trimToEmpty(taskInfo.getJSONObject("EXT_CONF").getString("agentId")));
							}
							//兜底技能组
							if(StringUtils.isNotBlank(taskInfo.getJSONObject("EXT_CONF").getString("extGroupId"))){
								userData.put("extGroupId", StringUtils.trimToEmpty(taskInfo.getJSONObject("EXT_CONF").getString("extGroupId")));
							}
							
							//1 预览式 2 自动外呼  3 IVR任务
							if("3".equals(taskInfo.getString("TASK_TYPE"))){
								JSONObject custObj = getTaskCustInfo(entId,taskId,row);
								if(custObj == null) custObj = new JSONObject();
								custObj.put("batchId", row.getColumnValue("BATCH_ID"));
								if(Constants.isBase64()) {
									userData.put("custInfo", Base64.getEncoder().encodeToString(custObj.toJSONString().getBytes()));
								}else{
									userData.put("custInfo", custObj);
								}
								//cly20231208多带一个id,为了区分机器人录音和转人工录音.机器人任务才带
								userData.put("robotSerialId", RandomKit.randomStr());
							}
							//
							//企业是否已经上传了坐席订购语音
							if(StringUtils.isNotBlank(context.getAgentOrderVox())){
								userData.put("agentOrderPrefix", context.getAgentOrderPrefix());  //坐席订购IVR字冠前缀，格式：前缀+企业ID
								userData.put("agentOrderVox", context.getAgentOrderVox());//订购流程语音
								userData.put("agentOrderCallback", context.getMarsGwUrl()+"/callback");  //订购结果通知回调地址
								userData.put("agentOrderCommand", "respAgentOrder");   // 回调订购命令
							}
							
							task.put("userData", userData);
							
							boolean validResult = true;
							try {
								String serviceList = Constants.getServiceList();
								if(StringUtils.isNotBlank(serviceList)){
									String[] services = serviceList.split(",");
									for(String service:services){
										JSONObject resultObj = recordValidCheck(service,row.toJSONObject());
										if(resultObj!=null){
											String   result = StringUtils.trimToEmpty(resultObj.getString("result"));
											String   reason = StringUtils.trimToEmpty(resultObj.getString("reason"));
											if(StringUtils.isNotBlank(result)){
												if(!"000".equals(result)){
													validResult = false;
													try {
														sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")
																+ " set TASK_STATE = 9  , SUBMIT_TIME = ? ,CALL_RESULT = ? , OBJ_MEMO = ? where ENT_ID = ? and  TASK_ID = ?  and  OBJ_ID  = ? ";
														QueryFactory.getTaskWriteQuery(entId).executeUpdate(sql, new Object[] { cal.getDateTime("-"),result, reason,entId, taskId, objId});
													} catch (Exception ex) {
														TaskLogger.getLogger().error(Constants.GATE_SERVER+ "taskGetData error,cause:"+ ex.getMessage(),ex);
													}
												}
											}
											
										}
									}
									
								}
							} catch (Exception ex) {
								TaskLogger.getLogger().error(ex,ex);
							}
							
							if(!validResult) continue;
							
							sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ") + " set TASK_STATE = 1 , SUBMIT_TIME = ?   where  OBJ_ID = ? ";
							QueryFactory.getTaskWriteQuery(entId).executeUpdate(sql, new Object[] {cal.getDateTime("-"),  objId});
							tasks.add(task);
						}
		
						// 任务总数，从任务中获取。
						sql = "select OBJ_COUNT,OBJ_USE_COUNT from  " + EntContext.getContext(entId).getTableName( "CC_TASK") + "	 where TASK_ID = ? ";
						JSONObject taskCount = QueryFactory.getTaskWriteQuery(entId).queryForRow(sql, new Object[] { taskId },new JSONMapperImpl());
						
						totalCount 	 = taskCount.getIntValue("OBJ_COUNT");  //任务总数
						int useCount = taskCount.getIntValue("OBJ_USE_COUNT");  //已使用数
						if(totalCount - useCount > 0){  //如果总数大于使用数，则代表还有数据，ocs继续发送taskGetData  fix by tzc ,20211209
							overplusCount = 1 ; 
						}
		                //实现批量update
						if (tasks.size() > 0) {
							overplusCount = 1 ;    //返回1代表还有数据要执行。
							if(totalCount==0) totalCount = tasks.size();
						}
					} catch (Exception ex) {
						TaskLogger.getLogger().error("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"]-> 获取任务数据失败,cause:" + ex.getMessage(), ex);
					}
			}
		
		}else{
			if(!isTargetOCS){
			    TaskLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] 当前请求获取数据的OCS节点["+clientId+"]和当前企业服务的OCS节点["+context.getOcsName()+"]不同,不返回任务数据...");
			}
			if(!isTaskWorktime){
				overplusCount = 1;
				TaskLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] -> 非任务配置的工作时间，不返回外呼数据...");
			}
			if(!isEntWorktime){
				overplusCount = 1;
				TaskLogger.getLogger().info("<taskGetData"+clientIdName+">["+taskId+","+taskInfo.getString("TASK_NAME")+"] -> 非企业配置的工作时间，不返回外呼数据...");
			}
		}
		
		data.put("validWorktime", (isTaskWorktime && isEntWorktime));   //工作时间
		data.put("fetchCount", tasks.size());  //当前要执行的任务书
		data.put("totalCount", totalCount);   //任务总记录数
		data.put("overplusCount", overplusCount);  //剩余外呼记录数
		
//		if(ServerContext.isDebug()) DBLogger.info(entId,"OCS-taskGetData", "OCS-taskGetData",taskInfo.getString("TASK_NAME"), "5分钟定时监控,期间执行次数："+getTaskDataCount, "entId:"+entId+"->"+data.toJSONString());
//		
//		if(tasks.size()>0){
//			DBLogger.info(entId,"OCS-taskGetData", "OCS-taskGetData",batchId,taskInfo.getString("TASK_NAME"),data.toJSONString());
//			//TaskLogger.getLogger().info(Constants.GATE_SERVER+ "taskGetData(entId:"+entId+", taskId:"+taskId+") - << Content:"+ data.toJSONString());
//		}
		data.put("taskRecords", tasks);
	    return data;
	}
	
	
	
	/**
	 * 外呼数据有效性测试,如果返回false,则不做外呼处理，直接设置成完成
	 * @return
	 */
	private JSONObject recordValidCheck(String serviceId,JSONObject taskObj){
		try {
			if(StringUtils.isBlank(serviceId)) return null;
			IService service = ServiceContext.getService(serviceId);
			if(service == null) return null;
			return service.invoke(taskObj);
		} catch (Exception ex) {
			TaskLogger.getLogger().error("Run recordValidCheck("+serviceId+") error,cause:"+ex.getMessage(),ex);
		}
		return null;
		
	}
	
	/**
	 * 获得企业信息，包括：坐席、技能组、技能组坐席
	 * @param entId
	 * @return
	 * @throws Exception
	 */
	private JSONObject getEntConfigInfo(String entId) throws Exception{
		readQuery.setMaxRow(10000);
		JSONObject data = new JSONObject();
		
		EntContext entContext = EntContext.getContext(entId);
		
		String sql =  "select t1.ENT_ID ,t1.ENT_CODE,t1.ENT_NAME,t1.FEE_CODE,t2.CALL_LICENSE,t2.AUTO_CALL_LICENSE,t2.AGENT_LICENSE "
				+ "from CC_ENT t1 , CC_ENT_RES  t2   where t2.ENT_ID  = ? and t1.ENT_ID = ? ";
		EasyRow entInfoRow = readQuery.queryForRow(sql, new Object[]{entContext.getResEntId(),entContext.getResEntId()});
		
		JSONObject entInfo = new JSONObject();
		entInfo.put("entId",  entId);
		//entInfo.put("entName", entContext.getEntName());
		entInfo.put("entName", entId);
		entInfo.put("entCode", entContext.getEntCode());
		entInfo.put("feeCode", entInfoRow.getColumnValue("FEE_CODE"));
		entInfo.put("outboundLicense", entInfoRow.getColumnValue("CALL_LICENSE"));
		entInfo.put("autoOutboundLicense", entInfoRow.getColumnValue("AUTO_CALL_LICENSE"));
		entInfo.put("agentphoneLicense", entInfoRow.getColumnValue("AGENT_LICENSE"));
		
		data.put("entInfo", entInfo);
		
		//获取技能组信息
		sql = "select SKILL_GROUP_ID,SKILL_GROUP_NAME,ENT_ID,QUEUE_STRATEGY  from "+EntContext.getContext(entId).getTableName( "CC_SKILL_GROUP")+" where ENT_ID = ?";
		
		List<EasyRow> rows = readQuery.queryForList(sql, new Object[]{entId});
		JSONArray skillGroups = new JSONArray();
		for(EasyRow row:rows){
			JSONObject item = new JSONObject();
			item.put("skillGroupId", row.getColumnValue("SKILL_GROUP_ID"));
			//item.put("skillGroupName", row.getColumnValue("SKILL_GROUP_NAME"));
			item.put("skillGroupName", row.getColumnValue("SKILL_GROUP_ID"));
			item.put("entId", row.getColumnValue("ENT_ID"));
			item.put("policy", row.getColumnValue("QUEUE_STRATEGY"));
			item.put("loadStatus", "0");
			skillGroups.add(item);
		}
		data.put("skillgroupInfos", skillGroups);
		
		
		//获取坐席信息
		sql = "select ENT_ID,USER_ID,USERNAME,ENT_ID,AGENT_PHONE,USER_ACCT,USER_PWD from CC_USER  where  USER_STATE = 0    and ADMIN_FLAG = 0 and  ENT_ID = ?";
		
		rows = readQuery.queryForList(sql, new Object[]{entId});
		JSONArray agentInfos = new JSONArray();
		for(EasyRow row:rows){
			JSONObject item = new JSONObject();
			item.put("agentId", row.getColumnValue("USER_ACCT"));  //这里送给平台的是坐席工号，例如：8001
			item.put("agentName", row.getColumnValue("USERNAME"));
			item.put("entId", row.getColumnValue("ENT_ID"));
			item.put("password", row.getColumnValue("USER_PWD"));
			item.put("loginReadyFlag", row.getColumnValue("0"));
			item.put("agentAcct", row.getColumnValue("USER_ACCT"));
			item.put("loadStatus", "0");
			agentInfos.add(item);
		}
		data.put("agentInfos", agentInfos); 
		
		//获取技能组信息
		//sql = "select SKILL_GROUP_ID,USER_ID,ENT_ID,IDX_ORDER from "+this.getTableName(entId, "CC_SKILL_GROUP_USER")+"  t1  where    ENT_ID = ?";
		
		sql = "select t1.* ,t2.AGENT_PHONE,t2.USER_ACCT from "+entContext.getTableName( "CC_SKILL_GROUP_USER")+"   t1 , CC_USER t2"
				+ " where t1.ENT_ID = t2.ENT_ID and t1.USER_ID = t2.USER_ID and t2.USER_STATE = 0  and t2.ENT_ID = ?";
	
		rows = readQuery.queryForList(sql, new Object[]{entId});
		JSONArray skillgroupAgents = new JSONArray();
		for(EasyRow row:rows){
			JSONObject item = new JSONObject();
			item.put("skillGroupId", row.getColumnValue("SKILL_GROUP_ID"));
			item.put("agentId", row.getColumnValue("USER_ACCT"));
			item.put("priority", row.getColumnValue("IDX_ORDER"));
			item.put("entId", row.getColumnValue("ENT_ID"));
			skillgroupAgents.add(item);
		}
		data.put("skillgroupAgents", skillgroupAgents);
		
		
		
		DBLogger.info(entId,"ICCS-loadEntInfo", "ICCS-loadEntInfo", entId, "加载企业信息",  "企业信息："+entInfo.toJSONString()+
				",技能组数："+skillGroups.size()+",坐席数："+agentInfos.size()+"，技能组坐席数："+skillgroupAgents.size()+",数据包长度："+data.toJSONString().length());
		return data;
	}
	
	
	/**
	 * 获得真实的被叫,通过外部服务
	 * @param jsonObject
	 * @return
	 */
	private String getRealCalled(JSONObject jsonObject){
		IService service = null;
		try {
			String serviceId = Constants.getRealCalledService();
			if(StringUtils.isBlank(serviceId)) return "";
			service = ServiceContext.getService(serviceId);
			if(service == null) return "";
			JSONObject result = service.invoke(jsonObject);
			if(result == null) return null;
			return result.getString("called");
		} catch (Exception ex) {
			TaskLogger.getLogger().error(ex,ex);
		}
		return "";
	}
	
	/**
	 * 获得外显号码，通过外部服务
	 * @param entId  企业Id
	 * @param taskId 任务Id
	 * @param skillId 技能组Id
	 * @param called  被叫号码
	 * @return
	 */
	private String getRealCaller(String entId,String taskId,String skillId,String called,String areacode,String objId){
		IService service = null;
		try {
			String serviceId = Constants.getRealCallerService();
			if(StringUtils.isBlank(serviceId)) return "";
			service = ServiceContext.getService(serviceId);
			if(service == null) return "";
			JSONObject params = new JSONObject();
			params.put("entId", entId);
			params.put("taskId", taskId);
			params.put("called", called);
			params.put("skillId", skillId);
			params.put("objId", objId);
			params.put("areacode", areacode);
			JSONObject result = service.invoke(params);
			if(result == null) return null;
			return result.getString("caller");
		} catch (Exception ex) {
			TaskLogger.getLogger().error(ex,ex);
		}
		return "";
	}
	
	
	/**
	 * 获得任务的任务信息
	 * @param taskId
	 * @param row 从cc_task_obj中获取的客户资料信息。
	 * @return
	 */
	private JSONObject getTaskCustInfo(String entId,String taskId,EasyRow  row){
		String tempInfo 	= cache.get("TASK_TEMP_"+taskId);
		JSONObject tempObj	=  null;
		try {
			
			if(StringUtils.isNotBlank(tempInfo)){
				tempObj =  JSONObject.parseObject(tempInfo);
			}else{
				String sql = "select TEMP_ID from "+EntContext.getContext(entId).getTableName( "CC_TASK")+"  where  TASK_ID = ? ";
				String tempId = QueryFactory.getTaskWriteQuery(entId).queryForString(sql, new Object[]{taskId});
				sql = " select CUST_ID,CUST_NAME,CUST_GROUP_NAME,TEL_NUM1,TEL_NUM2,TEL_NUM3,"
						+ " TEL_NUM4,F1,F2,F3,F4,F5,F6,F7,F8,F9,F10,F11,F12,F13,F14,F15,F16,F17,F18,F19,F20 "
						+ " from "+EntContext.getContext(entId).getTableName("cc_cust_temp")+" where TEMP_ID = ?";
				JSONObject jsonObject=QueryFactory.getTaskWriteQuery(entId).queryForRow(sql, new Object[]{tempId}, new JSONMapperImpl());
				tempObj = new JSONObject();
				//TaskLogger.getLogger().info("row->"+row.toJSONObject());
				for (String key : jsonObject.keySet()) {
					String jsonStr = jsonObject.getString(key);
					if(StringUtils.isNotBlank(jsonStr)){
						JSONObject _json = JSONObject.parseObject(jsonStr);
						//TaskLogger.getLogger().info(key+"->"+_json);
						//客户资料模板中，标注在用的字段,参考格式：{"readonly":"0","dataType":"0","name":"客户电话1","inuse":"1"}  inuse=1代表客户资料的该字段启用。
						if(_json.getInteger("inuse")==1){  
							tempObj.put(key,_json.getString("name"));
						}
					}
			    }
				cache.put("TASK_TEMP_"+taskId,tempObj.toJSONString(),300);
			}
			
		} catch (SQLException ex) {
			TaskLogger.getLogger().error(
					"getTaskCustInfo error  -> taskId:" + taskId + ",entId:" + entId + ",cause:" + ex.getMessage(), ex);
		}
		
		JSONObject custInfo = new JSONObject();
		
		for (String key : tempObj.keySet()) {
			//TaskLogger.getLogger().info(key+":"+tempObj.getString(key)+"->"+row.getColumnValue(key));
			if(StringUtils.containsAny(key, "TEL_NUM1","TEL_NUM2","TEL_NUM3","TEL_NUM4")){
				custInfo.put(tempObj.getString(key),PhoneCryptor.getInstance().decrypt(entId, row.getColumnValue(key)));
			}else{
				custInfo.put(tempObj.getString(key),row.getColumnValue(key));
			}
		}
		
		return custInfo;
	}
	
	
	/**
	 * 获得正在执行的任务信息
	 * @param entId
	 * @return
	 * @throws Exception
	 */
	private JSONObject getTaskList(String entId) throws Exception {

		// 获取技能组信息
		String sql = "select TASK_ID  from " + EntContext.getContext(entId).getTableName("CC_TASK")
				+ " where ENT_ID = ? and TASK_STATE = 5  and TASK_TYPE = 2 ";
		JSONObject data = new JSONObject();
		List<EasyRow> rows = readQuery.queryForList(sql, new Object[] { entId });
		JSONArray tasks = new JSONArray();
		for (EasyRow row : rows) {
			tasks.add(row.getColumnValue("TASK_ID"));
		}
		data.put("taskIdList", tasks);
		return data;
	}
	
	
	/**
	 * 获得任务信息，从memcache中获取，获取不到再从数据库中获取
	 * @param entId
	 * @param taskId
	 * @return
	 * @throws MessageException
	 */
//	private JSONObject getTaskinfo(String entId,String taskId) throws MessageException{  
//		
//		
//		//TaskLogger.getLogger().info("getTaskInfo()->taskId:"+taskId+",entId:"+entId);
//		try {
//			String taskObjeString = cache.get("TASK_"+taskId);
//			if(StringUtils.isNotBlank(taskObjeString)){
//				JSONObject taskObj =  JSONObject.parseObject(taskObjeString);
//				return taskObj;
//			}
//			String sql = "select BUSI_ORDER_ID,TASK_NAME,TASK_TYPE from "+EntContext.getContext(entId).getTableName( "CC_TASK")+"  where  TASK_ID = ? ";
//			EasyRow taskRow = readQuery.queryForRow(sql , new Object[]{taskId});
//			
//			JSONObject taskObj = new JSONObject();
//			taskObj.put("BUSI_ORDER_ID", taskRow.getColumnValue("BUSI_ORDER_ID"));
//			taskObj.put("TASK_NAME", taskRow.getColumnValue("TASK_NAME"));
//			taskObj.put("TASK_TYPE", taskRow.getColumnValue("TASK_TYPE"));
//			
//			sql = "select SKILL_GROUP_ID from "+EntContext.getContext(entId).getTableName( "CC_TASK_GROUP")+"  where  TASK_ID = ? ";
//			
//			String groupId = readQuery.queryForString(sql , new Object[]{taskId});
//			
//			if(StringUtils.isNotBlank(groupId)){
//				taskObj.put("SKILL_GROUP_ID", groupId);
//			}
//			
//			cache.put("TASK_"+taskId,taskObj.toJSONString(),300); //300秒更新一次缓存
//			return taskObj;
//		} catch (SQLException ex) {
//			 TaskLogger.getLogger().error("getTaskInfo error,taskId:"+taskId+" cuase:"+ex.getMessage(),ex);
//			 throw new MessageException(ex.getMessage());
//		}
//		
//	}
	
	private JSONObject getTaskinfo(String entId,String taskId){
		String taskObjeString = cache.get("getTaskinfo_"+taskId);
		if(StringUtils.isNotBlank(taskObjeString)){
			JSONObject taskObj =  JSONObject.parseObject(taskObjeString);
			return taskObj;
		}
		EntContext context = EntContext.getContext(entId);
		String sql = "select CALL_RATE_FACTOR,CALL_RATE,t1.PREFIX_NUM,t1.TASK_TYPE,t1.RES_COUNT,t1.PREFIX_GROUP_ID,t1.MAX_QUEUE_TIME, t1.TASK_STATE , "
				+ " t1.IVR_FLOW_NAME,t1.MAX_QUEUE_COUNT,t1.BUSI_ORDER_ID,t1.TASK_NAME,t1.EXT_CONF,"
				+ "	t2.SKILL_GROUP_ID,t3.VOX_PATH,t4.AUTO_CALL_LICENSE  from "+context.getTableName("CC_TASK")+" t1 "
				+ " left join CC_ENT_VOX t3  on t1.VOX_ID = t3.VOX_ID  "
				+ " left join "+context.getTableName("CC_TASK_GROUP")+ " t2  on t1.TASK_ID = t2.TASK_ID  "
						+ ",CC_ENT_RES t4  where   t1.TASK_TYPE in(2,3)  and t4.ENT_ID = ?    and t1.ENT_ID = ? and t1.TASK_ID = ?";
		JSONObject  taskInfo  = new JSONObject();
		try {
			TaskLogger.getLogger().info("getTaskInfo("+entId+","+taskId+")->"+sql);
//			taskInfo =  QueryFactory.getTaskWriteQuery(entId).queryForRow(sql, new Object[]{entId,entId,taskId},new JSONMapperImpl());
			//机构企业无企业资源，只能查找运营中心（父级企业）的资源
			taskInfo =  QueryFactory.getTaskWriteQuery(entId).queryForRow(sql, new Object[]{context.getResEntId(),entId,taskId},new JSONMapperImpl());
		} catch (SQLException ex) {
			TaskLogger.getLogger().error("获取企业["+entId+"]任务["+taskId+"]信息失败，原因："+ex.getMessage(),ex);
		}
		if(taskInfo == null) {
			TaskLogger.getLogger().error("获取企业["+entId+"]任务["+taskId+"]信息失败，原因：查询返回结果为。SQL->"+sql);
			return new JSONObject();
		}
		
		String extConf = taskInfo.getString("EXT_CONF");
		if(StringUtils.isNotBlank(extConf)){
			taskInfo.put("EXT_CONF", JSONObject.parseObject(extConf));
		}
		
		sql = "select VOX_PATH from CC_ENT_VOX  where ENT_ID = ? and  VOX_TYPE = 2";
		try {
			String queueFilePath =  QueryFactory.getWriteQuery(entId).queryForString(sql, new Object[]{entId});
			if(!StringUtils.isBlank(queueFilePath)){
				taskInfo.put("QUEUE_VOX_PATH", queueFilePath);
			}
		} catch (Exception ex) {
			TaskLogger.getLogger().error("获取企业["+entId+"]任务["+taskId+"]信息失败，原因："+ex.getMessage(),ex);
		}
		
		if("3".equals(taskInfo.getString("TASK_TYPE"))){
			sql = "select * from  CC_ROBOT_MODEL  where ROBOT_ID = ?";
			try {
				EasyRow row = QueryFactory.getWriteQuery(entId).queryForRow(sql, new Object[]{taskInfo.getString("IVR_FLOW_NAME")});
				if(row != null){
					taskInfo.put("ROBOT_MODEL_ID", row.getColumnValue("ROBOT_MODEL_ID"));
					taskInfo.put("ROBOT_FLOWNAME", row.getColumnValue("ROBOT_FLOWNAME"));
				}else{
					taskInfo.put("ROBOT_MODEL_ID", "");
					taskInfo.put("ROBOT_FLOWNAME", "");
				}
			} catch (SQLException ex) {
				TaskLogger.getLogger().error("获取企业["+entId+"]任务["+taskId+"]机器人信息失败，原因："+ex.getMessage(),ex);
			}
		}
		
		cache.put("getTaskinfo_"+taskId,taskInfo.toJSONString(),30); //30秒更新一次缓存
		
		return taskInfo;
	}
	
	
	
	/**
	 * 企业监控
	 * @param ents  企业信息 格式：
	 *  entId	String	必填	企业ID
		entName	String	必填	企业名称
		entCode	String	必填	企业编码
		logonAgentCount	String	必填	登陆坐席数
		idleAgentCount	String	必填	空闲坐席数
		busyAgentCount	String	必填	示忙坐席数
		alertAgentCount	String	必填	振铃坐席数
		talkAgentCount	String	必填	通话坐席数
		workNotReadyAgentCount	String	必填	话后整理坐席数
		
	在消息对象中：
	{
		"clientId": "iccs",
		"command": "iccsMonitorEnt",
		"data": "{\"entMonitors\":}",
		"serialId": "000162E6625FEDC3",
		"sign": "",
		"timestamp": "20220420192555",
		"version": "1.0",
		"via": "http://172.16.82.6:9059/petradatagw/interface"
	}
	
	其中entMonitors结构如下：
	{
		"entId": "1001",
		"entName": "",
		"entCode": "",
		"logonAgentCount": "0",
		"idleAgentCount": "0",
		"pdsboundIdleAgentCount": "0",
		"inboundIdleAgentCount": "0",
		"outboundIdleAgentCount": "0",
		"busyAgentCount": "0",
		"alertAgentCount": "0",
		"talkAgentCount": "0",
		"workNotReadyAgentCount": "0"
	}

	 * @throws Exception
	 */
	private void   entMonitor(JSONArray ents) throws Exception{
		EasyCalendar calendar = EasyCalendar.newInstance();
		for(int i = 0 ;i<ents.size();i++){
			JSONObject jsonObject = ents.getJSONObject(i);
			jsonObject.put("updateTime",calendar.getDateTime("-"));
			CacheManager.getMemcache().put("ICCS_ENT_UPDATE_"+jsonObject.getString("entId"), System.currentTimeMillis()+"", CACHE_TIME);  //缓存时间为300秒,启动任务的时候需要清理cache
			CacheManager.getMemcache().put("ICCS_ENT_"+jsonObject.getString("entId"), jsonObject, CACHE_TIME);
		}
	}
	
	
	
	/**
	 * 技能组监控
	 * @param skillGroups 格式：
	 * skillGroupId	String	必填	技能组ID
		skillGroupName	String	必填	技能组名称
		entId	String	必填	企业ID
		logonAgentCount	String	必填	登陆坐席数
		idleAgentCount	String	必填	空闲坐席数
		busyAgentCount	String	必填	示忙坐席数
		alertAgentCount	String	必填	振铃坐席数
		talkAgentCount	String	必填	通话坐席数
		workNotReadyAgentCount	String	必填	话后整理坐席数
	 * @throws Exception
	 */
	private void   skillGroupMonitor(JSONArray skillGroups) throws Exception{
		EasyCalendar calendar = EasyCalendar.newInstance();
		for(int i = 0 ;i<skillGroups.size();i++){
			JSONObject jsonObject = skillGroups.getJSONObject(i);
			jsonObject.put("updateTime",calendar.getDateTime("-"));
			CacheManager.getMemcache().put("ICCS_SKILLGROUP_"+jsonObject.getString("skillGroupId"), jsonObject, CACHE_TIME);  //缓存时间为300秒,启动任务的时候需要清理cache
		}
	}
	
	
	/**
	 * 监控磐石的任务执行状态，是否和云呼的状态一致，不一致，则执行taskNotify的命令。
	{
		"entId": "2000002049",
		"TaskList": "[{\"jtRate\":\"0.000000\",\"ocsTotalDialedCount\":\"0\",\"entId\":\"2000002049\",\"idleAgentCount\":\"12\",\"10jtRate\":\"0.000000\",\"lastCallTime\":\"1970-01-01 00:00:00\",\"callCount\":\"0\",\"errorLog\":\"\",\"stateReason\":\"2\",\"ocsTaskId\":\"17\",\"logTime\":\"2021-12-10 08:56:17\",\"timeStamp\":\"1639098239\",\"maxOccupyCount\":\"0\",\"objCount\":\"0\",\"ocsTotalSucceedCount\":\"0\",\"ocsLast10minTotalDialedCount\":\"0\",\"lastGetTaskTime\":\"2021-12-10 00:00:37\",\"state\":\"2\",\"taskId\":\"83633956650269927229870\",\"systemBusyCount\":\"0\",\"mark\":\"1\",\"ocsLast10minTotalSucceedCount\":\"0\"}]"
	}
	 [{
		"jtRate": "0.000000",
		"ocsTotalDialedCount": "0",
		"entId": "2000002049",
		"idleAgentCount": "12",
		"10jtRate": "0.000000",
		"lastCallTime": "1970-01-01 00:00:00",
		"callCount": "0",
		"errorLog": "",
		"stateReason": "2",
		"ocsTaskId": "17",
		"logTime": "2021-12-10 08:56:17",
		"timeStamp": "1639098239",
		"maxOccupyCount": "0",
		"objCount": "0",
		"ocsTotalSucceedCount": "0",
		"ocsLast10minTotalDialedCount": "0",
		"lastGetTaskTime": "2021-12-10 00:00:37",
		"state": "2",
		"taskId": "83633956650269927229870",
		"systemBusyCount": "0",
		"mark": "1",
		"ocsLast10minTotalSucceedCount": "0"
	}]

	 * @param taskList
	 * @throws SQLException 
	 */
	private void   taskMonitor(String clientId,JSONObject jsonObject) throws Exception{
		
		//if(ServerContext.isDebug()){
		OCSLogger.getLogger().info("<taskMonitor> << " + jsonObject.toJSONString());
		TaskLogger.getLogger().info("<taskMonitor> << " + jsonObject.toJSONString());
		//}
		String entId = jsonObject.getString("entId");
//		if(!jsonObject.containsKey("TaskList")){
//			TaskLogger.getLogger().warn("<taskMonitor> JSON数据缺少TaskList字段，请检测OCS请求参数！");
//			return;
//		}
		JSONArray taskList = jsonObject.getJSONArray("TaskList");
		if(taskList == null) taskList = new JSONArray();
		EasyCalendar cal = EasyCalendar.newInstance();
		Set<String> taskSet = new HashSet<String>();
		
		String sql = "select TASK_ID,TASK_NAME from "+ EntContext.getContext(entId).getTableName("CC_TASK")+" where ENT_ID = ? and TASK_TYPE  in(2,3,5)  and   TASK_STATE = 5 ";
		List<JSONObject> tasks = QueryFactory.getTaskWriteQuery(entId).queryForList(sql, new Object[]{entId},new JSONMapperImpl());
		
		//获得当前企业正在执行的任务列表
		for(JSONObject task:tasks){
			taskSet.add(task.getString("TASK_ID"));
		}
		
		TaskLogger.getLogger().info("<taskMonitor>["+entId+"] 获取数据库中的启动任务列表 -> "+taskSet);
		OCSLogger.getLogger().info("<taskMonitor>["+entId+"] 获取数据库中的启动任务列表 -> "+taskSet);
		
		try {
			CacheUtil.hset("#ocs", "clientId", clientId);
			CacheUtil.hset("#ocs", "taskMonitorTime", EasyCalendar.newInstance().getDateTime("-"));
			CacheUtil.hset("#ocs", "taskMonitorTimestamp", System.currentTimeMillis()+"");
		} catch (Exception ex) {
			OCSLogger.getLogger().error(ex,ex);
			TaskLogger.getLogger().error(ex,ex);
		}
		
		//如果非启动状态的任务，不做暂停通知处理。　1.停止 2.暂停 3.启动
		for(int i = 0 ;i<taskList.size();i++){
			JSONObject petraTask = taskList.getJSONObject(i);
			JSONObject taskInfo = this.getTaskinfo(entId, petraTask.getString("taskId"));
			String  taskName = taskInfo.getString("TASK_NAME");
			//如果任务名称都没有，则代表这个任务非暂停和启动中的状态，则通知ocs执行任务停止。
			if(StringUtils.isBlank(taskName)){  //停止已经在云呼中状态为非（暂停和启动中的任务）
				TaskLogger.getLogger().info("<taskMonitor>["+petraTask.getString("taskId")+"] -> 任务已经被删除，发送taskNotify[1：停止]通知OCS停止任务。");
				this.taskNotify(entId, petraTask.getString("taskId"), "1",petraTask.getString("state"),"不在电销的任务列表中，通知OCS停止任务"); //停止
				continue;
			}
			
			//如果任务处于暂停状态，如果非工作时间，则不做处理。  --　1.停止 2.暂停 3.启动
			if("2".equals(petraTask.getString("state"))){
				if(!CheckWorkTimeService.isWorkTime(entId, cal.getDateTime("-"),  taskInfo.getString("BUSI_ORDER_ID"))){
					//非工作时间，移除正在执行的任务ID，针对这个任务不做任何处理,这个时候，OCS和电销的任务状态是不同步的。
					OCSLogger.getLogger().info( "<taskMonitor>["+petraTask.getString("taskId")+","+taskName+"][非企业工作时间，OCS任务状态为2:pause的不做处理]...");
					TaskLogger.getLogger().info( "<taskMonitor>["+petraTask.getString("taskId")+","+taskName+"][非企业工作时间，OCS任务状态为2:pause的不做处理]...");
					taskSet.remove(petraTask.getString("taskId"));  
					continue;
				}
				if(!CheckWorkTimeService.isTaskWorkTime(taskInfo.getJSONObject("EXT_CONF"), cal.getDateTime("-"), petraTask.getString("taskId"), taskName)){
					OCSLogger.getLogger().info( "<taskMonitor>["+petraTask.getString("taskId")+","+taskName+"][非任务工作时间，OCS任务状态为2:pause的不做处理]...");
					TaskLogger.getLogger().info( "<taskMonitor>["+petraTask.getString("taskId")+","+taskName+"][非任务工作时间，OCS任务状态为2:pause的不做处理]...");
					taskSet.remove(petraTask.getString("taskId"));  //非工作时间，移除正在执行的任务ID，针对这个任务不做任何处理。
					continue;
				}
				//如果OCS是暂停状态，云呼是执行状态，根据上面的判断已经是在工作时间内的，则执行启动任务。
				if(taskSet.contains(petraTask.getString("taskId"))){ //启动OCS任务（任务在云呼正常执行状态中）
					this.taskNotify(entId, petraTask.getString("taskId"), "3",petraTask.getString("state"),"OCS任务状态为暂停[2],电销任务状态为启动中[3],通知OCS启动任务");
					taskSet.remove(petraTask.getString("taskId"));  //非工作时间，移除正在执行的任务ID，针对这个任务不做任何处理。
					continue;
				}
			}
			
			
			if("3".equals(petraTask.getString("state"))) {
				//如果OCS的启动任务不在电销的任务执行列表中，则发送暂停任务
				if(!taskSet.contains(petraTask.getString("taskId"))){  //不止执行列表中，暂停的OCS任务
					this.taskNotify(entId, petraTask.getString("taskId"), "2",petraTask.getString("state"),"不在电销启动任务列表中，通知OCS暂停任务");   // --　1.停止 2.暂停 3.启动
					continue;
				}
				
				//非工作时间，暂停任务。
				if(!CheckWorkTimeService.isWorkTime(entId, cal.getDateTime("-"),  taskInfo.getString("BUSI_ORDER_ID"))){
					TaskLogger.getLogger().info("<taskMonitor>["+taskInfo.getString("TASK_NAME")+"] -> 非企业工作时间，发送taskNotify[2：暂停]通知OCS暂停任务。");
					this.taskNotify(entId, petraTask.getString("taskId"), "2",petraTask.getString("state"),"非企业工作时间暂停任务，通知OCS暂停任务");   // --　1.停止 2.暂停 3.启动
					taskSet.remove(petraTask.getString("taskId"));  //非工作时间，移除正在执行的任务ID，针对这个任务不做任何处理。
					 //非工作时间，移除正在执行的任务ID，针对这个任务不做任何处理。
					continue;
				}
				
				//非工作时间，暂停任务。
				if(!CheckWorkTimeService.isTaskWorkTime(taskInfo.getJSONObject("EXT_CONF"), cal.getDateTime("-"), petraTask.getString("taskId"), taskInfo.getString("TASK_NAME"))){
					TaskLogger.getLogger().info("<taskMonitor>["+taskInfo.getString("TASK_NAME")+"] -> 非任务工作时间，发送taskNotify[2：暂停]通知OCS暂停任务。");
					this.taskNotify(entId, petraTask.getString("taskId"), "2",petraTask.getString("state"),"非任务工作时间暂停任务，通知OCS暂停任务");  //  --　1.停止 2.暂停 3.启动
					taskSet.remove(petraTask.getString("taskId"));  //非工作时间，移除正在执行的任务ID，针对这个任务不做任何处理。
					continue;
				}
				
				taskSet.remove(petraTask.getString("taskId"));
			}
			
//			try {
//				if("3".equals(petraTask.getString("state"))){
//					EasyRecord record = new EasyRecord(Constants.getStatSchema()+".CC_RPT_OCS_MONITOR", "TASK_ID");
//					record.setPrimaryValues(petraTask.getString("taskId"));
//					record.set("TASK_NAME", taskInfo.getString("TASK_NAME"));
//					record.set("ENT_ID", entId);
//					record.set("ENT_NAME", EntContext.getContext(entId).getEntName());
//					record.set("DATA_TIME", petraTask.getString("lastGetTaskTime"));
//					record.set("CALL_TIME", petraTask.getString("lastCallTime"));
//					record.set("RES_COUNT", petraTask.getIntValue("callCount"));
//					record.set("JT_RATE", petraTask.getFloatValue("jtRage"));
//					record.set("OBJ_COUNT", petraTask.getIntValue("objCount"));
//					record.set("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
//					record.set("UPDATE_TIMESTAMP", System.currentTimeMillis()/1000);
//					if(StringUtils.isNotBlank(petraTask.getString("stateDesc"))){
//						record.set("STATE_DESC", petraTask.getString("stateDesc"));
//					}
//					if(!readQuery.update(record)){
//						readQuery.save(record);
//					}
//				}
//			} catch (Exception ex) {
//				TaskLogger.getLogger().error("<taskMonitor> -> 更新监控信息失败，原因："+ex.getMessage()+",监控信息:"+petraTask.toJSONString());
//			}
			
//			//磐石的任务已经在云呼的启动任务列表中的，则针对改任务不做处理。
//			if(taskSet.contains(petraTask.getString("taskId"))){ 
//				taskSet.remove(petraTask.getString("taskId"));
//			}else{ //如果磐石任务，不在云呼的在启动任务列表中，则执行暂停任务处理。
//				if(!"3".equals(petraTask.getString("state"))) continue;  //如果非启动状态的任务，不做暂停通知处理。　1.停止 2.暂停 3.启动
//				TaskLogger.getLogger().info("<taskMonitor>["+petraTask.getString("taskId")+"] -> 任务已经被删除，发送taskNotify[1：停止]通知OCS停止任务。");
//				this.taskNotify(entId, petraTask.getString("taskId"), "1"); //停止
//			}
		}
		
		//这些任务不在OCS任务列表中的部分，执行启动状态。
		Iterator<String> iter = taskSet.iterator();
		while(iter.hasNext()){
			String taskId = iter.next();
			JSONObject taskInfo = this.getTaskinfo(entId, taskId);
			if(!CheckWorkTimeService.isWorkTime(entId, cal.getDateTime("-"),  taskInfo.getString("BUSI_ORDER_ID"))) {
				OCSLogger.getLogger().info( "<taskMonitor>["+taskId+","+taskInfo.getString("TASK_NAME")+"][非企业工作时间不发送启动任务通知到OCS]");
				continue;
			}
			if(!CheckWorkTimeService.isTaskWorkTime(taskInfo.getJSONObject("EXT_CONF"), cal.getDateTime("-"), taskId, taskInfo.getString("TASK_NAME"))){
				OCSLogger.getLogger().info( "<taskMonitor>["+taskId+","+taskInfo.getString("TASK_NAME")+"][非任务工作时间不发送启动任务通知到OCS]");
				continue;
			}
			TaskLogger.getLogger().info("<taskMonitor>["+taskInfo.getString("TASK_NAME")+"] -> 其中状态的任务不在OCS的监控任务列表中，发送taskNotify[3：启动]通知OCS启动任务。");
			this.taskNotify(entId, taskId, "3","NULL","任务不在OCS执行任务中，通知OCS启动任务");
		}
	}
	
	
//	/**
//	 * 检查已经启动的任务，但在OCS的任务列表中没有的任务，重新发送启动到OCS
//	 * @param ocsTaskSet
//	 */
//	public void taskStartCheck(String entId,Set<String> ocsTaskSet){
//		List<JSONObject> startTaskList = new ArrayList<JSONObject>();
//		List<JSONObject>  taskList = getStartTaskList(entId);
//		for (JSONObject taskObj:taskList) {
//			if(!ocsTaskSet.contains(taskObj.getString("TASK_ID"))){
//				startTaskList.add(taskObj);
//			}
//		}
//		for(JSONObject taskObj:startTaskList){
//			this.taskNotify(taskObj.getString("ENT_ID"), taskObj.getString("TASK_ID"), "3");
//		}
//	}
//	
//	private  List<JSONObject> getStartTaskList(String entId){
//		try {
//			EntContext context = EntContext.getContext(entId);
//			String sql = "select ENT_ID,TASK_ID  from "+context.getTableName("CC_TASK")+" where TASK_STATE = 5  and  TASK_TYPE in (2,3,5) ";
//			List<JSONObject>  taskList = QueryFactory.getWriteQuery("2").queryForList(sql, new Object[]{},new JSONMapperImpl());
//			return taskList;
//		} catch (Exception ex) {
//			TaskLogger.getLogger().error(ex,ex);
//		}
//		return new ArrayList<JSONObject>();
//	}
//	
//	
//	/**
//	 * 获得当前配置的数据库
//	 * @return
//	 */
//	protected List<String>  getSchemas(){
//		List<String>  schemas = new ArrayList<String>();
//		String sql = "select SCHEMA_ID  from  CC_SCHEMA_RES ";
//		List<EasyRow> rows;
//		try {
//			rows = QueryFactory.getReadQuery().queryForList(sql, new Object[]{});
//			for(EasyRow row:rows){
//				schemas.add(row.getColumnValue("SCHEMA_ID"));
//			}
//		} catch (Exception ex) {
//			TaskLogger.getLogger().info(ex ,ex);
//		}
//		
//		return schemas;
//	}
	/**
	 * 执行任务通知
	 * @param entId
	 * @param taskId
	 * @param status
	 */
	private void taskNotify(String entId,String taskId,String status,String srcStatus,String reason){
		IService service;
		try {
			service = ServiceContext.getService("YC-TASK-NOTIFY-SERVICE");
			JSONObject  jsonIn = new JSONObject();
			jsonIn.put("entId", entId);
			jsonIn.put("taskId", taskId);
			jsonIn.put("status", status);
			jsonIn.put("src", "auto");
			JSONObject taskInfo = this.getTaskinfo(entId, taskId);
			String taskName = taskInfo.getString("TASK_NAME");
			if(StringUtils.isBlank(taskName)) taskName = "已删除任务："+taskId;
			//　1.停止 2.暂停 3.启动
			String desc = "";
			if("1".equals(status)) desc =  "1:stop" ;
			if("2".equals(status)) desc =  "2:pause";
			if("3".equals(status)) desc =  "3:start";
			jsonIn.put("desc", desc);
			service.invoke(jsonIn);
			TaskLogger.getLogger().info("<taskNotify>["+desc+"]["+taskId+","+taskName+"][OCS任务状态："+srcStatus+"]["+reason+"]->"+jsonIn);
			OCSLogger.getLogger().info( "<taskNotify>["+desc+"]["+taskId+","+taskName+"][OCS任务状态："+srcStatus+"]["+reason+"]->"+jsonIn);
		} catch (Exception ex) {
			OCSLogger.getLogger().error(
					"run taskNotify error  -> taskId:" + taskId + ",entId:" + entId + ",cause:" + ex.getMessage(), ex);
			TaskLogger.getLogger().error(
					"run taskNotify error  -> taskId:" + taskId + ",entId:" + entId + ",cause:" + ex.getMessage(), ex);
		}
		
	
	}
	
	
	/**
	 * 4.17	SIP话机状态上送接口(磐石网关提供)
	 * command->sipPhoneMonitorNotify
	 * 参数：
	 * ipmgInfo	Ipmg	必填	获得ipmg的基本信息，详细见Ipmg
		sipPhones	SipPhone[]	必填	本次获取到的SipPhone话机数组
		数组元素类型请查看公共数据对象中的SipPhone定义

		Ipmg参数定义
		参数名	参数类型	是否必填	参数说明
		ipmgName	String	必填	Ipmg应用名称
		ipmgStationNo	String	必填	ipmg站点号
		ipmgIpAddress	String	必填	ipmg的ip和端口，格式ip:port

		SipPhone参数定义
		参数名	参数类型	是否必填	参数说明
		account	String	必填	帐号
		password	String	必填	密码
		entId	String	必填	企业ID
		sbcIpAddress	String	必填	Sbc的ip和端口，格式ip:port

{
	"clientId": "ipmg",
	"command": "sipPhoneMonitorNotify",
	"data": "{\"ipmgInfo\":{\"ipmgName\":\"IPMG\",\"ipmgStationNo\":\"7\",\"ipmgIpAddress\":\"*************:5070\"},\"sipPhones\":[{\"account\":\"888004\",\"status\":\"ON_LINE\",\"sbcIpAddress\":\"*************:5030\",\"entId\":\"\"}]}",
	"serialId": "073D030C5FAA5B83003D030C5FF51ECC",
	"sign": "",
	"timestamp": "2021-01-06 10:22:04.001",
	"version": "1",
	"via": "http://*************:9059/yc-api/interface"
}

	 * @param impgInfo
	 * @param sipPhones
	 */
	private void sbcMonitor(JSONObject impgInfo,JSONArray sipPhones){
		
		EasyCalendar cal = EasyCalendar.newInstance();
		
		Map<String,JSONArray>  sbcMap = new HashMap<String,JSONArray>();
		
		for (int idx = 0; idx < sipPhones.size(); idx++) {
			JSONObject sip = sipPhones.getJSONObject(idx);
			JSONArray sbc = sbcMap.get(sip.getString("sbcIpAddress"));
			if(sbc == null) {
				sbc = new JSONArray();
				sbcMap.put(sip.getString("sbcIpAddress"), sbc);
			}
			sbc.add(sip);
		}
		
		JSONObject sbcList = cache.get("SBC_LIST");  //这里是保存SBC的信息
		if(sbcList==null){  //如果不存在，创建新的
			sbcList= new JSONObject();
		}
		//遍历所有的SBC地址。
		Iterator<String> sbcKeys = sbcMap.keySet().iterator();
			
		while(sbcKeys.hasNext()){
			String  key = sbcKeys.next();
			JSONObject sbcObject = new JSONObject();
			sbcObject.put("sbc", key);
			sbcObject.put("count", sbcMap.get(key).size());
			sbcObject.put("ipmg", impgInfo.getString("ipmgIpAddress"));
			sbcObject.put("timestamp", System.currentTimeMillis());
			sbcObject.put("updateTime", cal.getDateTime("-"));
			sbcList.put(key, sbcObject);
			if(ServerContext.isDebug()) MonitorLogger.getLogger().info("SBC_"+key+"->"+sbcMap.get(key));
			cache.put("SBC_"+key,sbcMap.get(key));  //缓存SBC的数据 ，这里是保存SBC里所有的key的数据。
		}
		
		Iterator<String> iter = sbcList.keySet().iterator();
		JSONObject _sbcList =  new JSONObject();  //这里是保存SBC的信息
		
		while(iter.hasNext()){
			String key = iter.next();
			JSONObject sbcObject = sbcList.getJSONObject(key);
			long timer = sbcObject.getLongValue("timestamp");
			if(System.currentTimeMillis() - timer  <  60*1000){ //如果状态更新
				_sbcList.put(key, sbcObject);
			}
		}
		
		if(ServerContext.isDebug()) MonitorLogger.getLogger().info("SBC_LIST->"+_sbcList);
		cache.put("SBC_LIST", _sbcList,600);
	}
	
	/**
	 * 通过外部服务[GET_ORIGCALL_SERVICE]来获取原被叫、原主叫的信息
	 * @param entId  企业ID
	 * @param caller  主叫
	 * @param called  被叫
	 * @param  robotId 机器人ID
	 * @param  createCause 呼叫创建原因
	 * @return   {"origCaller":"133001","origCalled":"138001"}
	 */
	private JSONObject getOrigCallInfo(String entId,String caller,String called,String robotId,int createCause){
		IService service;
		JSONObject params = new JSONObject();
		try {
			//fix cly：先判断是否存在，减少打印告警日志
			List<String> findByPrefix = ServiceContext.findByPrefix("GET_ORIGCALL_SERVICE");
			if(findByPrefix == null || findByPrefix.size() == 0){
				return new JSONObject();
			}
			service = ServiceContext.getService("GET_ORIGCALL_SERVICE");
			if(service == null) return new JSONObject();
			params.put("entId",entId );
			params.put("caller",caller );
			params.put("called",called );
			params.put("robotId",robotId );
			params.put("createCause",createCause );
			params.put("busiType","api");
			JSONObject result =  service.invoke(params);
			if(result == null) result = new JSONObject();
			TaskLogger.getLogger().info("GET_ORIGCALL_SERVICE(param:"+params.toJSONString()+")->"+result);
			return result;
		} catch (ServiceException ex) {
			TaskLogger.getLogger().error("GET_ORIGCALL_SERVICE(param:"+params.toJSONString()+") run error,cause:"+ex.getMessage(),ex);
		}
		return new JSONObject();
	}
	
	private String getOrigCalled(String entId,String caller,String called,String robotId,int createCause){
		JSONObject result = this.getOrigCallInfo(entId,caller,called,robotId,createCause);
		if(result == null) return "";
		return result.getString("origCalled");
	}

}
