package com.yunqu.yc.api.sysc;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.base.QueryFactory;
import com.yunqu.yc.api.listener.GlobalContextListener;

/**
 * 缓存处理类
 * <AUTHOR>
 */
public abstract class BaseSysc implements Runnable{
	private static EasyQuery query = QueryFactory.getWriteQuery(null);

	/**
	 * 批次大小2000
	 */
	private static int batchSize = 2000;
	
	/**
	 * 超时时间
	 */
	public static int timeout = 2*24*3600;
	
	/**
	 * 操作数据总数
	 */
	private int count = 0;
	
	/**
	 * 运行状态
	 */
	private boolean runState = false;

	protected EasyCache cache = CacheManager.getMemcache();

	@Override
	public void run() {
		if(runState){
			getLogger().info(this.getClassName()+"->正在运行，不可重复操作");
			return;
		}
		runState = true;
		count = 0;
		this.syncData();
		runState = false;
	}
	
	/**
	 * 设置处理类名
	 */
	public abstract String getClassName();
	/**
	 * 设置查询语句
	 */
	public abstract String[] getQuerySql();
	
	/**
	 * 同步前缀处理
	 */
	public abstract void syscPreData();
	
	/**
	 * 同步过程中处理业务数据
	 */
	public abstract void parseData(JSONObject jsonObject);

	/**
	 * 同步结束处理逻辑
	 */
	public abstract void syscEndData();
	
	/**
	 * 同步数据
	 */
	protected void syncData() {
		String[] querySql = this.getQuerySql();
		if(querySql == null || querySql.length == 0){
			getLogger().info(this.getClassName()+"->查询语句不能为空");
			return;
		}
		long timer = System.currentTimeMillis();
		getLogger().info(this.getClassName()+"->开始执行数据同步到cache...");
		try {
			//数据初始化操作
			this.syscPreData();
			for (String sql : querySql) {
				if(!GlobalContextListener.runState){
					break;
				}
				getLogger().info(this.getClassName()+"->执行脚本："+sql);
				//同步处理数据操作
				count += this.excuteData(sql);
			}
			//数据结果处理操作
			this.syscEndData();
			getLogger().info(this.getClassName()+"->缓存数据成功，已经同步记录数："+count+",用时:"+((System.currentTimeMillis()-timer)/1000)+"秒");
		} catch (Exception ex) {
			this.syscEndData();
			getLogger().error(this.getClassName()+"->缓存数据失败，原因："+ex.getMessage(),ex);
		}
	}
	
	/**
	 * 同步处理
	 * @param sql
	 */
	private int excuteData(String sql){
		int excuteCount = 0;
		if(StringUtils.isBlank(sql)){
			return excuteCount;
		}
		List<JSONObject> list = null;
		int len = batchSize;
		try {
			int index = 1;
			while(GlobalContextListener.runState && len == batchSize){
				list = this.getQuery().queryForList(sql, new Object[]{}, index, batchSize , new JSONMapperImpl());
				for (JSONObject rs : list) {
					if(!GlobalContextListener.runState){
						break;
					}
					this.parseData(rs);
				}
				index++;
				len = list.size();
				excuteCount += len;
				this.getLogger().info(this.getClassName()+"->正在同步数据，已经同步记录数："+(excuteCount+count));
				Thread.sleep(50);
			}
		} catch (Exception e) {
			this.getLogger().error(this.getClassName()+"->缓存数据失败，原因："+e.getMessage(), e);
		}
		return excuteCount;
	}

	/**
	 * 获取日志源
	 * @return
	 */
	protected Logger getLogger(){
		return LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME+"_syscCache");
	}
	
	/**
	 * 获取数据源
	 * @return
	 */
	protected EasyQuery getQuery(){
		return query;
	}
}
