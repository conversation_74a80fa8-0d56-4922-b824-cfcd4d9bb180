<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>任务导入的数据</title>
	<style type="text/css">
		#dataList td{display: none;}
		#dataList tr td{white-space:nowrap;min-width:100px;max-width:300px;text-overflow:ellipsis;overflow:hidden}
	    .nodata,.text-center{display: table-cell!important;}
	    .autoFill td{display: table-cell!important;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		   <form class="form-inline" id="searchForm">
					<input type="hidden" name="taskId" value="${param.taskId }"/>
					<input type="hidden" name="tempId" value="${param.tempId }"/>
					<div class="ibox">
						<div class="ibox-title clearfix">
							 <div class="form-group">
								  <h5> 客户名单列表</h5>
								  <c:if test="${empty param.batchId }">
									  <div class="input-group input-group-sm" style="width: 150px">
										  <span class="input-group-addon">批次</span>
										  <select class="form-control input-sm" data-value="${param.batchId }" name="batchId" id="batchId" data-mars="taskObj.batchDict" data-mars-top="true" onchange="TaskCustList.loadData()">
											  <option value="">--请选择--</option>
										  </select>
									  </div>
								  </c:if>
								  <c:if test="${!empty param.batchId }">
									<input type="hidden" name="batchId" value="${param.batchId }"/>
								  </c:if>
								  <div class="input-group input-group-sm">
									  <span class="input-group-addon">名单状态</span>
									  <select data-value="${param.taskState}" class="form-control input-sm" name="taskState">
										  <option value="">全部</option>
										  <option selected="selected" value="9">已<span class="portal-name">营销</span></option>
										  <option value="0">未使用</option>
										  <option value="5">预约中</option>
										  <c:if test="${param.type==2 }"> <option value="1">等待执行中</option></c:if>
									  </select>
								  </div>
								  <div class="input-group input-group-sm saleResult" style="width: 150px;display: none;">
									  <span class="input-group-addon"><span class="portal-name">营销</span>结果</span>
									  <select data-mars="task.taskResultDict" data-value="${param.resultId }" class="form-control input-sm" name="resultId">
										  <option value="">全部</option>
									  </select>
								  </div>
									<div class="input-group input-group-sm">
									  <span class="input-group-addon">呼叫结果</span>
									  <select data-value="${param.callResult}" class="form-control input-sm" name="callResult">
											<option value="">全部</option>
											<option value="0">成功</option>
											<option value="1">无人应答</option>
											<option value="2">用户忙</option>
											<option value="3">用户挂机</option>
											<option value="4">网络忙</option>
											<option value="5">空号</option>
											<option value="6">拒接</option>
											<option value="7">关机</option>
											<option value="8">停机</option>
											<option value="9">不在服务区</option>
											<option value="10">传真机</option>
											<option value="11">欠费</option>
											<option value="12">重复号码</option>
											<option value="13">电话总机</option>
											<option value="14">久叫不应</option>
											<option value="98">坐席挂机</option>
											<option value="99">系统错误</option>
											<option value="100">其它呼叫失败</option>
									  </select>
									</div>
								  <div class="input-group input-group-sm">
										  <span class="input-group-addon">客户编号</span>
										  <input type="text" name="custId" class="form-control input-sm" style="width:90px">
								   </div>
								  <div class="input-group input-group-sm">
										  <span class="input-group-addon">客户号码</span>
										  <input type="text" name="custPhone" class="form-control input-sm" style="width:110px">
								   </div>
								  <div class="input-group input-group-sm hidden">
										  <span class="input-group-addon">客户名称</span>
										  <input type="text" name="custName" class="form-control input-sm" style="width:90px">
								   </div>
								  <div class="input-group input-group-sm saletimediv" style="width: 120px">
	             		              <span class="input-group-addon">执行时间</span>
									  <input type="text" name="beginSaleTime" id="beginSaleTime" data-mars-top="true" data-mars="common.todayBegin" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endSaleTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:128px" autocomplete="off">
	             		              <span class="input-group-addon">-</span>	
									  <input type="text" name="endSaleTime" id="endSaleTime" data-mars-top="true" data-mars="common.todayEnd" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginSaleTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:128px" autocomplete="off">
								  </div>
								  <c:if test="${empty param.batchId }">
									  <div class="input-group input-group-sm" style="width: 120px">
										  <span class="input-group-addon">导入时间</span>
										  <input type="text" name="beginTime" id="beginTime" data-mars-top="true" data-mars="common.todayBegin" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',minDate:'#F{$dp.$D(\'endTime\',{d:-90})}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:128px">
										  <span class="input-group-addon">-</span>
										  <input type="text" name="endTime" id="endTime" data-mars-top="true" data-mars="common.todayEnd" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:128px">
									  </div>
								  </c:if>
								   <div class="input-group input-group-sm">
										  <button type="button" class="btn btn-sm btn-default" onclick="TaskCustList.loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								   </div>
								   <div class="input-group input-group-sm pull-right">
										  <button type="button" class="btn btn-sm btn-success btn-outline" onclick="TaskCustList.exp()"><span class="glyphicon glyphicon-export"></span> 导出 </button>
								   </div>
							  </div>
						</div>
						<div class="ibox-content table-responsive">
							<table id="taskcust"></table>
						</div>
					</div>
			</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
		jQuery.namespace("TaskCustList");
		$(function(){
			  requreLib.setplugs('wdate,layui',function(){
					$("#searchForm").render({success:function(result){
						$("select[name='taskState']").change(function(){
							if($(this).val()==9){
								$(".saleResult").show();
								$(".saletimediv").show();
								$('#beginSaleTime').removeAttr('disabled');
								$('#endSaleTime').removeAttr('disabled');
							}else{
								$(".saleResult").hide();
								$(".saletimediv").hide();
								$('#beginSaleTime').attr('disabled','disabled');
								$('#endSaleTime').attr('disabled','disabled');
							}
						});
						 requreLib.setplugs('layui',function(){
							 initTable();
						 });
					}});
			  })
		});
		function initTable(){
			ajax.remoteCall("${ctxPath}/webcall?action=taskObj.header",form.getJSONObject("#searchForm"),function(result) {
				 var headerData=result;
				 if(headerData){
					var data=headerData.data;
					var cols={};
					for(var index in data){
						var rowObj=data[index];
						if(rowObj['inuse']=='0'){
							rowObj['hide']=true;
						}
						data[index]['templet'] = (function(){
					        var field = data[index].field;
					        var dataType = data[index].dataType;
					        return function(row){
					           var val=row[field];
					           if(dataType=='phone'){
					        	   return advReplace(val,-4,3,'*');
					        	   //return getPhone(val,row["_"+field]);
					           }else if(dataType=='idcard'){
					        	   return advReplace(val,10,4,'*');
					           }else if(dataType=='userName'){
					        	   return advReplace(val,1,3,'*');
					           }else if(dataType=='hide'){
					        	   return '--';
					           }
					           if(field.indexOf('TEL_NUM')>-1){
					        	   return getPhone(val,row["_"+field]);
					           }
					          return val;
					        };
						})(index); 
						cols[index]=rowObj;
					} 
					var defaultOpts=[
					{title:'批次名称',field:'BATCH_NAME'}
					,{title:'名单状态',field:'TASK_STATE',templet:function(row){
						var val = getText(row.TASK_STATE,'taskState');
						if(val){
							return val;
						}
						return '-';
					}}
					,{title:'外呼成功数',field:'SUCC_CALL_TIMES'}
					,{title:'外呼次数',field:'CALL_TIMES'}
					,{title:'通话时长（秒）',field:'TOTAL_TIME'}
					,{title:'外呼时间',field:'SALE_FINISH_TIME'}
					,{title:'呼叫结果',field:'CALL_RESULT',templet:function(row){
						if(row.CALL_RESULT){
							var val = getText(row.CALL_RESULT,'callResult');
							if(val){
								return val;
							}
						}
						return '-';
					}}
					];
					
					defaultOpts.push({title:'操作',field:'OBJ_ID',templet:function(row){
						var html = '';
						if(row.TASK_STATE < 2){
							html = '<a href="javascript:void(0)" onclick="TaskCustList.custInfo(\''+row.OBJ_ID+'\',\''+row.TASK_ID+'\')">查看</a>'
						}else{
							html = '<a href="javascript:void(0)" onclick="TaskCustList.robotInfo(\''+row.OBJ_ID+'\',\''+row.TASK_ID+'\')">查看</a>'
						}
						return html;
					}})
					
					var newData=data.concat(defaultOpts);
					 $("#searchForm").initTable({
							id:'taskcust',
							height:'full-200',
							title:'客户名单',
							mars:'taskObj.custData',
							cellMinWidth:120,
							cols: [newData]
					 	 }
					 );
						 
				 }
			});
		}
		TaskCustList.loadData=function(){
		    $("#searchForm").queryData();
		}
		
		TaskCustList.custInfo = function (objId,taskId){
			popup.layerShow({type:1,title:"信息",offset:'lb',shadeClose:false,area:['25%','100%'],maxmin:true,anim: 2,shade :0},"${ctxPath}/pages/task/task-obj-edit.jsp",{objId:objId,taskId:taskId});
		}
		
		TaskCustList.robotInfo = function (objId,taskId) {
            popup.layerShow({type: 1, maxmin: true, anim: 0, scrollbar: false, offset: 'r', area: ['800px', '100%'], url: '${ctxPath}/pages/task/robot/task-detail.jsp',title: '详情', data: {objId: objId, taskId: taskId},
                cancel: function (index, layero) {
                    window.wavesurfer && wavesurfer.destroy();
                }
            });
        }
		
		TaskCustList.exp = function(){
			var pageSize = $('.layui-laypage-count').html();
			if(pageSize == undefined || pageSize == ''){
				layer.alert('列表数据为空，不能导出！',{icon: 7});
				return;
			}
			pageSize = pageSize.split(' ')[1];
			layer.confirm('是否导出客户名单列表？',{icon: 3, title:'导出提示',offset:'20px'}, function(index){
				layer.close(index);
				TaskCustList.exportAll(pageSize);
				//location.href = "${ctxPath}/servlet/export?action=exportCustList&"+$("#searchForm").serialize();
			});
		}

		TaskCustList.exportAll = function(pageSize){
			var data = form.getJSONObject("#searchForm");
			data["pageIndex"]= 1;
			data["pageSize"]= pageSize;
			var exData1 = [];
			ajax.remoteCall("${ctxPath}/webcall?action=taskObj.custData",data, function (res) {
			if(res.data&&res.data.length>0){
				exData1 = res.data;
        		layui.use('table', function(){
           		  	var table = layui.table;
           		  	table.exportFile('taskcust',exData1 ,'xls');
              	})
        	}
		  });
		}
	</script>
	
	
	
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
