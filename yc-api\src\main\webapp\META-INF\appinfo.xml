<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="yc-api" name="云呼平台API接口程序" package-by="ljp46" package-time="2025-07-16 17:36:48" version="3.3.0#20250716-1">
    <datasources>
         <datasource description="主数据库(1)" isnull="true" name="yc-write-ds-1"/>
        <datasource description="主数据库(2)" isnull="true" name="yc-write-ds-2"/>
        <datasource description="读数据源" isnull="true" name="yc-read-ds"/>
    </datasources>
    
    <description>
		3.3.0#20250716-1
			1、优化多线程调度。
			2、优化EntContext.getContext()方法和EntContext.getTableInfo()方法频繁堵塞的问题。
			3、优化磐石心跳定时通知睡眠时间过长的问题。
			4、增加多线程调度测试用例。
    	3.3.0#20250219-1 1、处理getTableInfo锁的问题
    </description>
	<versionHistory>
		3.3.0#20241204-2 1、漏话添加默认值
		3.3.0#20241125-1 1、调整漏话接口，兼容硬化机写入未接的情况；2、调整号码加密逻辑，兼容第三方加密方式；3、调整判断字段是否存在逻辑，拓展可用性；4、添加OCS_ENTS记录使用任务的企业；
		3.3.0#20240911-1 1、白名单添加失效时间判断
		3.3.0#20240903-1 1、启动任务时候，修改任务状态；
		3.3.0#20240819-1 1、 添加机器人意向等字段字段，新加脚本在/yc-api/WebContent/META-INF/script/update.sql
		3.3.0#20240904-1 1、适配老版本sipCallId写不进userData从vuale里面取
		3.3.0#20240805-1 1、设置ocs获取任务数据超时10秒后马上返回所得的数据
		3.3.0#20240722-1 1、格式化被叫号码原码；2、兼容ivr请求callback时把消息内容放到body;
		3.3.0#20240613-1 1、把号码拦截功能抽成一个服务，对外提供业务功能；
		3.3.0#20240323-1 1、新增任务优先级；新增字段alter table cc_task add PRIORITY int default 1 comment '任务优先级，数字越大优先级越高，5 高 3 中 1 低';
		3.3.0#20240320-1 1、增加任务执行结果通知外部服务的处理线程数到50，通知个数到50000，增加通知执行时间打印。
		3.3.0#20240129-1 1、改变黑名单读取方式；2、修改从缓存读取的数据；
		3.3.0#20240102-1 1、把robotSerialId只放在机器人任务里
		3.3.0#20231218-1 1、优化初始化EntContext，解决获取漏话、未接、通话录音表的字段信息查询慢的问题，增加分页查询可以解决该问题。
		3.3.0#20231208-1 1、OCS外呼时，多带一个robotSerialId作为机器人录音ID，留callSerialId作为机器人转人工时的录音ID；2、拓展任务分表范围TaskObjTableUtils，
		3.3.0#20231102-1 1、解决redis异常时候，同步数据到redis会出现死循环问题。
		3.3.0#20231011-1 1、OCS回传结果的时候，增加recordFileName字段。
		3.3.0#20230809-1 1、OCS外呼结果写话单增加用户的振铃时间和振铃时长。  2、机器人外呼结果写话单增加用户的振铃时间和振铃时长，需要机器人流程把userAlertingTime送过来。
		3.3.0#20230601-1 1、增加支持电销任务对象相关的分表存储。
		3.3.0#20230412-1 1、漏话增加增加坐席话机振铃的类型。
		3.3.0#20230329-1 1、修改坐席查询服务，兼容优先查询通话记录后查询坐席信息；2、机器人结果保存添加地区编码；3、机器人呼叫失败也能更新结果；
		3.3.0#20230316-1 1、应用配置中，增加是否运行呼叫红名单的开关。 2、针对OCS回调结果的通知采用异步方式执行，避免主线程的阻塞。
		3.3.0#20230314-1 1、增加OCS外呼场景下互损重呼的配置开关，互损定义,呼叫结果为： 120 转接坐席失败 16 坐席振铃超时 17 坐席振铃挂断 18 坐席振铃用户挂断
		3.3.0#20230309-1 1、优化云呼运行状态检查，增加对磐石网关消息收发是否正常的检查。2、处理IVR的漏话，增加VIDEO_FILE 和 RECORD_FILE 语音和视频文件的值。  3、话单表增加ROBOT_ID记录这个话单属于哪个机器人。
		3.3.0#20230306-1 1、机器人失败结果，需要更改cc_task_obj_robot记录信息；
		3.3.0#20230309-2 1、优化云呼运行状态检查，增加对磐石网关消息收发是否正常的检查。2、处理IVR的漏话，增加VIDEO_FILE 和 RECORD_FILE 语音和视频文件的值。  3、话单表增加ROBOT_ID记录这个话单属于哪个机器人。
		3.3.0#20230302-1 1、增加对任务外呼的外显号码每小时、每天的呼叫频次的限制支持。通过企业管理的外呼配置进行设置。
		3.3.0#20230228-1 1、解决OCS获取任务的时候，如果对应的外呼名单号码的Hcode不存在会导致处理任务名单数据失败，无法返回外呼数据给OCS的问题。  2、优化黑名单同步逻辑，应用重启时主动退出死循环。
		3.3.0#20230221-1 1、解决磐石多中心切换后，不往新的OCS退数据的问题。
		3.3.0#20230129-1 1、OCS第一次获取外呼名单数据，参数增加reset标志，如果reset的值为true，则mars对任务名单数据进行重置。
		3.3.0#20230109-1 1、企业资源同步接口，同步数据增加 云客服接入号(accessnumberList)，需在企业管理中-扩展配置中新增配置key “accessnumberList”以,间隔。
		3.3.0#20230105-1 1、机器人对话记录，增加keyword关键字，来指定是否命中敏感词，如果命中，这机器人监控哪里增加投诉预警展现。
		3.2.0#20221205-1 1、写漏话，增加原被叫和原主叫字段，需要在业务库中增加 ORIG_CALLER  和  ORIG_CALLED
		3.2.0#20221201-1 1、解决H码没用同步到缓存，导致外地号码不加0的问题；
	</versionHistory>
</application>
