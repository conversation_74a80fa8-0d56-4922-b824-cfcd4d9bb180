package com.yunqu.yc.api.executor.test;

import com.yunqu.yc.api.executor.AbstractExecutor;
import com.yunqu.yc.api.log.TaskLogger;

/**
 * 用于测试线程空闲超时的测试执行器
 * 执行完成后线程会进入空闲状态，用于测试120秒超时退出机制
 */
public class IdleTestExecutor extends AbstractExecutor {
    
    private String sessionId;
    private int executorId;
    private long executeTime; // 执行时间
    
    public IdleTestExecutor(String sessionId, int executorId, long executeTime) {
        this.sessionId = sessionId;
        this.executorId = executorId;
        this.executeTime = executeTime;
    }
    
    @Override
    public void execute() throws Exception {

        long startTime = System.currentTimeMillis();
        
        try {
            // 快速执行完成
            if (executeTime > 0) {
                Thread.sleep(executeTime);
            }

        } catch (InterruptedException e) {
            TaskLogger.getLogger().warn("[TEST] IdleTestExecutor[" + executorId + "] 被中断");
            Thread.currentThread().interrupt();
        }
    }
    
    @Override
    public String getSessionId() {
        return sessionId;
    }
    
    @Override
    public String toString() {
        return "IdleTestExecutor[id=" + executorId + ", sessionId=" + sessionId + 
               ", executeTime=" + executeTime + "ms]";
    }
}
