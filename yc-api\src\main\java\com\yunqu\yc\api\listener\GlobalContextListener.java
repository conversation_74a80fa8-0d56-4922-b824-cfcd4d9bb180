package com.yunqu.yc.api.listener;

import javax.servlet.ServletContextEvent;

import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.executor.EventDispatcher;
import com.yunqu.yc.api.log.ApiLogger;
import com.yunqu.yc.api.log.TaskLogger;
import com.yunqu.yc.api.service.DataCacheService;
import com.yunqu.yc.api.task.TaskResultHandler;

/**
 * 应用上下文监听器
 */
@WebListener
public class GlobalContextListener  implements ServletContextListener {

	private ThreadGroup  threadGroup;

	public static  boolean  runState =  true;

	/**
     * war被卸载时触发,在这个方法里针对应用被卸载时候做一些处理操作。
     */
    public void contextDestroyed(ServletContextEvent arg0)  {

    	runState = false;
		try {
			Thread.sleep(15000);
		} catch (InterruptedException e) {
		}

		try {
    		threadGroup.interrupt();
		} catch (Exception e) {
			// TODO: handle exception
		}

    	try {
    		threadGroup.destroy();
		} catch (Exception e) {
			// TODO: handle exception
		}

    	ApiLogger.getLogger().info("stop yc-api server ...");
    }

	/**
     * war应用加载时被触发，通常用于加载调度、数据源等。
     */
    public void contextInitialized(ServletContextEvent arg0)  {
    	ApiLogger.getLogger().info("start yc-api server ...");
		runState = true;
    	threadGroup =  Constants.getAppThreadGroup();
		try {
			Thread.sleep(5000);
		} catch (InterruptedException e) {
		}


//    	Thread pingThread = new Thread(threadGroup,new PetraPingService());
//    	pingThread.start();
//
//
//    	Thread entNotifyThread = new Thread(threadGroup,new EntUpdateNotifyService());
//    	entNotifyThread.start();
//
//    	Thread taskNotifyThread = new Thread(threadGroup,new TaskNotifyService());
//    	taskNotifyThread.start();

//    	Thread timerThread = new Thread(threadGroup,new PetraPingService());
//    	timerThread.start();
//
//    	Thread sdrSyncService = new Thread(threadGroup,new SdrSyncService());
//    	sdrSyncService.start();
    	
    	
    	try {
			Thread  thread = new Thread(threadGroup,new EventDispatcher(), Constants.APP_NAME+"_EventDispatcher");
			thread.start();
		} catch (Exception ex) {
			TaskLogger.getLogger().error("GlobalContextListener.contextInitialized() error,cause:" + ex.getMessage(), ex);
		}

    	try {
    		Thread cacheSyncService = new Thread(threadGroup,new DataCacheService(), Constants.APP_NAME+"_DataCacheService");
        	cacheSyncService.start();
		} catch (Exception ex) {
			ApiLogger.getLogger().error("GlobalContextListener.contextInitialized() error,cause:" + ex.getMessage(),ex);
		}

		/* 该功能在本模块废弃，功能迁移至alarm-mgr 20220425
    	try {
    		Thread statService = new Thread(threadGroup,new AlarmMonitorService());
    		statService.start();
		} catch (Exception ex) {
			ApiLogger.getLogger().error("GlobalContextListener.contextInitialized() error,cause:" + ex.getMessage(),ex);
		}*/


//    	try {
//			for(int i= 0 ;i<=9 ;i++){
//				Thread indexThread = new Thread(threadGroup,new TaskResultHandler(i), Constants.APP_NAME+"_TaskResultHandler"+i);
//				indexThread.start();
//			}
//		} catch (Exception ex) {
//			ApiLogger.getLogger().error("GlobalContextListener.contextInitialized() error,cause:" + ex.getMessage(),ex);
//		}


    }
}
