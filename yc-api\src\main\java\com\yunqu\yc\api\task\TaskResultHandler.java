package com.yunqu.yc.api.task;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.base.QueryFactory;
import com.yunqu.yc.api.context.EntContext;
import com.yunqu.yc.api.context.TaskContext;
import com.yunqu.yc.api.executor.EventDispatcher;
import com.yunqu.yc.api.executor.impl.NotifyExecutor;
import com.yunqu.yc.api.listener.GlobalContextListener;
import com.yunqu.yc.api.log.TaskLogger;
import com.yunqu.yc.api.util.CacheUtil;
import com.yunqu.yc.api.util.HCodeUtil;
import com.yunqu.yc.api.util.TaskObjTableUtils;
import org.easitline.common.core.activemq.MessageException;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 处理OCS外呼执行结果通知，原来是通过MQ发送到消息队列通过yc-stat进行处理，现在在yc-api里面完成处理。
 * 该消息不管外呼成功还是失败，不管是是否转机器人都会有收到通知
 * <AUTHOR>
 *
 */
@Deprecated
public class TaskResultHandler implements Runnable {
	
	int index;
	
	private EasyCache cache = CacheManager.getMemcache();
	
	private static  BlockingQueue<JSONObject> blockingQueue = new LinkedBlockingQueue<>(50000);
	
	public static void addNotify(JSONObject  jsonObject){
		try {
			boolean offer = blockingQueue.offer(jsonObject, 100, TimeUnit.MILLISECONDS);
			if(!offer){
				TaskLogger.getLogger().warn("TaskResultHandler.addNotify() -> blockingQueue.offer()=false : "+jsonObject);
			}
		} catch (Exception e) {
			TaskLogger.getLogger().error(e.getMessage(),e);
		}
	}
	
	public TaskResultHandler(int index){
		this.index = index;
	}
	
	@Override
	public void run() {
		while(GlobalContextListener.runState){
		    try {
		    	JSONObject  jsonObject  = blockingQueue.poll(3000, TimeUnit.MILLISECONDS);
		    	if(jsonObject!=null){
		    		this.onMessage(jsonObject);
		    	}
			} catch (Exception ex) {
				TaskLogger.getLogger().error(ex,ex);
				try {
					Thread.sleep(2000);
				} catch (Exception e) {
					return;
				}
			}
		}
	}
	
	/**
	 * 主叫号码计数器，按天，按小时计数。
	 * @param caller
	 */
	private void addCallerCount(String caller){
		try {
			EasyCalendar cal = EasyCalendar.newInstance();
			int day = cal.getDateInt();
			int hour = cal.getCalendar().get(EasyCalendar.HOUR);
			String dayKey = "marscall_"+day+"_"+caller;
			String hourKey = "marscall_"+hour+"_"+caller;
			String dayCallCount = CacheUtil.get(dayKey);
			String hourCallCount = CacheUtil.get(hourKey);
			
			if(StringUtils.isBlank(dayCallCount)) {
				dayCallCount = "1";
			}else{
				dayCallCount = (Integer.parseInt(dayCallCount)+1)+"";
			}
			if(StringUtils.isBlank(hourCallCount)) {
				hourCallCount = "1";
			}else{
				hourCallCount = (Integer.parseInt(hourCallCount)+1)+"";
			}
			CacheUtil.put(dayKey, dayCallCount,36*3600);
			CacheUtil.put(hourKey, dayCallCount,7200);
		} catch (Exception ex) {
			TaskLogger.getLogger().error(ex,ex);
		}
	}
	
	/**
	 * {
	"callResultDesc": "Success",
	"callId": "218334259",
	"origCallResult": "0",
	"userAlertingTime": "2023-03-27 10:56:30",
	"sipCallId": "92ea6e8d3af9e7b53d90d16ffadbed0a1679655799-5060@***********",
	"userData": {
		"ocsSkillGroup": "13",
		"callSerialId": "83201142553886925988734",
		"entId": "1001",
		"queueFile": "",
		"maxCallIndex": 1,
		"displayCaller": "32255601",
		"batchId": "83201142554136926040170",
		"custPhone": "13380278728",
		"robotId": "",
		"flowName": "",
		"objId": "83201142539953021374578",
		"curCallIndex": 1,
		"callbackUrl": "http://***********:9059/yc-api/callback",
		"busiOrderId": "83805887038649888512913",
		"taskId": "83478503310056368180541",
		"realCalled": "13380278728",
		"welcomeFile": "",
		"maxRingTime": 30
	},
	"called": "013380278728",
	"ivrAgentTime": "1970-01-01 00:00:00",
	"entId": "1001",
	"failResonDesc": "",
	"ivrQueueTime": "1970-01-01 00:00:00",
	"ivrBeginTime": "2023-03-27 10:56:40",
	"caller": "32255601",
	"serialId": "83201142539953021374578",
	"createTime": "2023-03-27 10:56:30",
	"failResonCode": "",
	"callResult": "0",
	"taskId": "83478503310056368180541",
	"ivrEndTime": "2023-03-27 10:56:48"
}
	 * @param taskResult
	 * @throws Exception
	 */
	
	
	public void onMessage(JSONObject taskResult) throws Exception {
		
		TaskLogger.getLogger().info(" << "+taskResult);
		
		this.addCallerCount(taskResult.getString("caller"));
		
		JSONObject   task = null;
		String taskName =   null;
		EasyCalendar cal = EasyCalendar.newInstance();
		String robotSerialId = null;
		try {
			int callResult = taskResult.getIntValue("callResult");
			JSONObject userData = taskResult.getJSONObject("userData");
			if(userData == null) userData = new JSONObject();
			String entId = taskResult.getString("entId");
			String taskId = userData.getString("taskId");
			String objId = userData.getString("objId");
			int curCallIndex = 1;
			int maxCallIndex = 1;
			if(StringUtils.isNotBlank(userData.getString("curCallIndex"))){
				curCallIndex = Integer.parseInt(userData.getString("curCallIndex"));
			}
			if(StringUtils.isNotBlank(userData.getString("maxCallIndex"))){
				maxCallIndex = Integer.parseInt(userData.getString("maxCallIndex"));
			}
			if(curCallIndex >= maxCallIndex)   curCallIndex = 0;
			//cly20231208 机器人任务结果使用robotSerialId作为录音ID，保留callSerialId
			robotSerialId = userData.getString("robotSerialId");
			String callSerialId = userData.getString("callSerialId");
			if(StringUtils.isBlank(robotSerialId)){
				robotSerialId = callSerialId;
			}
			
			String called = userData.getString("realCalled");
			if(StringUtils.isBlank(entId) || StringUtils.isBlank(taskId) || StringUtils.isBlank(objId) ){
				TaskLogger.getLogger().error("[taskResultNotify-"+index+"] Param error,cause:param[entId:"+entId+",taskId:"+taskId+",objId:"+objId+"] is null,taskResult->"+taskResult);
				return;
			}
			
			task =  this.getTask(entId, taskId);
			
			taskName = task.getString("TASK_NAME");
			
			TaskLogger.getLogger().info("[taskResultNotify-"+index+"]["+taskName+"][blockingQueue:"+blockingQueue.size()+"] << "+taskResult);
			
			EasyQuery easyQuery = QueryFactory.getTaskWriteQuery(entId);
			
			try {
				
				EntContext   context = EntContext.getContext(entId);
				EasyCalendar calendar = EasyCalendar.newInstance(taskResult.getString("createTime"), "yyyy-MM-dd HH:mm:ss");
				if(StringUtils.isBlank(called)){
					called = taskResult.getString("called");
					if(called.length()>20){
						String bpoPrefix = StringUtils.trimToEmpty(context.getBpoPrefix());
				        if(StringUtils.isNotBlank(bpoPrefix)){
				        	called = called.substring(bpoPrefix.length()+5, called.length());
				        }
					}
				}
				
				String caller = taskResult.getString("caller");

				String calledAreaCode = StringUtils.trimToEmpty(HCodeUtil.getAracodeByCalled(entId, caller, called));
				
				called = HCodeUtil.formatCalled(entId, caller, called);
				
				//呼叫结束时间为空，则把ivrEndTime 改成呼叫创建时间。
				String ivrEndTime  = taskResult.getString("ivrEndTime");
				if(isBlankDateTime(ivrEndTime)) ivrEndTime  = taskResult.getString("createTime");
				String ivrBeginTime  = taskResult.getString("ivrBeginTime");
				if(isBlankDateTime(ivrBeginTime)) ivrBeginTime  = ivrEndTime;
				String userAlertingTime = taskResult.getString("userAlertingTime");
				int aleringTime = 0;
				//cly20231208 修改振铃时间计算方式，不管接通还是未接都要计算振铃时长
				if(callResult==0){
					if(isBlankDateTime(ivrBeginTime) || isBlankDateTime(userAlertingTime)){
						//
					}else{
						try {//振铃时长=进入ivr时间-开始振铃时间
							aleringTime = calendar.diff(userAlertingTime,ivrBeginTime, "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
						} catch (Exception ex) {
						}
					}
				}else{
					if(isBlankDateTime(ivrEndTime) || isBlankDateTime(userAlertingTime)){
						//
					}else{
						try {//振铃时长=ivr结束时间-开始振铃时间
							aleringTime = calendar.diff(userAlertingTime,ivrEndTime, "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
						} catch (Exception ex) {
						}
					}
				}
				
				EasyRecord record = new EasyRecord(context.getTableName("CC_CALL_RECORD"),"SERIAL_ID");
				
				String createTime = this.getCreateTime(taskResult.getString("createTime"));
				int totalTime = 0;
				if (isBlankDateTime(taskResult.getString("ivrBeginTime")) || isBlankDateTime(taskResult.getString("ivrEndTime"))) {
					//
				} else {
					try {// 总的通话时间=接通进入IVR时间-坐席释放时间
						totalTime = calendar.diff(taskResult.getString("ivrBeginTime"), taskResult.getString("ivrEndTime"),
								"yyyy-MM-dd HH:mm:ss", Calendar.SECOND);
					} catch (Exception ex) {
						TaskLogger.getLogger().error(ex,ex);
					}
				}
				
				String recordFileName = taskResult.getString("recordFileName");
				record.set("BUSI_CALL_ID", callSerialId);
				record.setPrimaryValues(robotSerialId);
				record.set("MONTH_ID", calendar.getFullMonth());
				record.set("DATE_ID", calendar.getDateInt());
				record.set("CREATE_TIME", createTime);
				record.set("BILL_BEGIN_TIME", ivrBeginTime);
				record.set("BILL_END_TIME", ivrEndTime);
				record.set("BILL_TIME", totalTime);
				if(!isBlankDateTime(userAlertingTime)){
					record.set("AGENT_STAY_TIME", aleringTime);  //外呼振铃时间，这里保存的用户的振铃时间
					record.set("AGENT_TIME", userAlertingTime); 
				}
				record.set("BUSI_ORDER_ID", userData.getString("busiOrderId"));
				record.set("ENT_ID", entId);
				record.set("OBJ_ID", objId); // 任务对象
				record.set("CREATE_CAUSE", 8); // 呼叫创建原因,8 智能外呼
				record.set("TASK_ID", task.getString("TASK_ID")); // 任务ID
				record.set("TASK_NAME", task.getString("TASK_NAME")); // 任务名称
				record.set("CLEAR_CAUSE", callResult); //挂机原因
				record.set("CALLER", caller); // 主叫
				record.set("CALLED", called); // 被叫
				String custPhone = StringUtils.isNotBlank(called)&&called.startsWith("0")?called.substring(1):called;
				record.set("CUST_PHONE", custPhone); // 客户号码，去0
				
				String skillId = userData.getString("ocsSkillGroup");
				
				if(StringUtils.isNotBlank(skillId)){
					record.set("GROUP_ID",skillId);
					try {
						String sql = "select SKILL_GROUP_NAME from "+EntContext.getContext(entId).getTableName( "CC_SKILL_GROUP")+" where SKILL_GROUP_ID = ?";
						String groupName = easyQuery.queryForString(sql, new Object[]{skillId});
						record.set("GROUP_NAME",groupName);
					} catch (Exception ex) {
						TaskLogger.getLogger().error(ex,ex);
					}
				}
				//fix by tzc ,20231011 ocs回传增加录音文件，失败也要有振铃录音文件。
				if(StringUtils.isNotBlank(recordFileName)){
					record.set("RECORD_FILE", recordFileName);
				}
				
				String sipCallId = taskResult.getString("sipCallId");
				String callId = taskResult.getString("callId");
				

				record.set("SIP_CALL_ID", sipCallId);
				
				if(StringUtils.isNotBlank(callId)){
					callId = EasyCalendar.newInstance().getDateInt() +"-" +callId;
					record.set("CALL_ID", callId);
				}
				
				JSONObject areaObj = HCodeUtil.getAreacode(custPhone);
				if(areaObj!=null){
					String areacode = StringUtils.trimToEmpty(areaObj.getString("area"));
					String vendor = StringUtils.trimToEmpty(areaObj.getString("vendor"));;  // '所属运营商,取值 0：电信  1：移动  2：联通'
					record.set("AREA_CODE",areacode);
					record.set("DATA2",vendor);
				}
				record.set("CALL_TIMESTAMP", System.currentTimeMillis()/1000);
				record.set("RES_TYPE", 1);
				String recordId = taskResult.getString("recordId");
				if(StringUtils.isNotBlank(recordId)){
					record.set("RECORD_ID",recordId);
				}
				record.set("AREA_CODE",calledAreaCode);

				record.set("ROBOT_ID", task.getString("IVR_FLOW_NAME"));
				
				try {
					String sql = "select count(1) as tcount from "+context.getTableName("CC_CALL_RECORD")+" where SERIAL_ID = ? ";
					boolean isRecordExist = easyQuery.queryForExist(sql, new Object[]{robotSerialId});
					if(!isRecordExist){
						easyQuery.save(record);
					}
				} catch (Exception ex) {
					TaskLogger.getLogger().error("[taskResultNotify-"+index+"]["+taskName+"] update task object result error ,cause:" + ex.getMessage(),ex);
				}
				
				String supperSdrEx =  AppContext.getContext("yc-stat").getProperty("SUPPORT_SDR_EX", "N");
				
				if("Y".equalsIgnoreCase(supperSdrEx)){
					record.put("schema", context.getSchemaId());
					EventDispatcher.addEvent(new NotifyExecutor("STAT-SDR-EX-",robotSerialId, record));
				}
				
				//保存机器人任务信息,更新任务执行信息
				if("3".equals(task.getString("TASK_TYPE"))){
					JSONObject   taskObject = this.getTaskObject(entId,taskId,objId);
					//cly20231208 支持根据任务分表
					record = new EasyRecord(TaskObjTableUtils.getObjRobotTableName(taskId,context.getSchemaId()),"OBJ_ID");
					record.setPrimaryValues(objId);
					record.set("ENT_ID", entId);
					record.set("MONTH_ID", calendar.getFullMonth());
					record.set("DATE_ID",  calendar.getDateInt());
					record.set("TASK_ID",  taskId);  //任务ID
					record.set("BATCH_ID", taskObject.getString("BATCH_ID"));  //客户编号
					record.set("CUST_ID",  taskObject.getString("CUST_ID"));  //客户编号
					record.set("CUST_NAME", taskObject.getString("CUST_NAME"));  //客户姓名、
					record.set("CUST_PHONE", custPhone); // 客户号码，去0
					record.set("SERIAL_ID", robotSerialId);  //通话记录ID
					record.set("BEGIN_TIME", getDateTime(taskResult.getString("ivrBeginTime"),taskResult.getString("createTime")));  //外呼开始时间
					record.set("END_TIME", getDateTime(taskResult.getString("ivrEndTime"),taskResult.getString("createTime")));  //外呼结束时间
					record.set("CALL_TIME", totalTime);  //通话时长
					record.set("CLEAR_CAUSE", callResult);  //挂机原因
					record.set("RECORD_FILE", recordFileName);
					record.set("READ_FLAG", "0");  //已读状态
					TaskLogger.getLogger().info("[taskResultNotify-"+index+"]["+taskName+"] callResult->"+callResult);
					if(callResult>0){
						//1 无人应答  2 用户忙  9 不在服务区 14 久叫不应
						if(callResult==1 || callResult==2 || callResult==9  || callResult==14){
							TaskLogger.getLogger().info("[taskResultNotify-"+index+"]["+taskName+"] STD_CLASS->E,callResult->"+callResult);
							record.set("STD_CLASS", "E");  //已读状态
						}else{
							TaskLogger.getLogger().info("[taskResultNotify-"+index+"]["+taskName+"] STD_CLASS->F,callResult->"+callResult);
							record.set("STD_CLASS", "F");  //已读状态
						}
						this.evedayTask(taskId, entId, robotSerialId, record);
						
						EntContext.getContext(entId).checkRecord(record);
						boolean bl = easyQuery.update(record);
						if(!bl){
							easyQuery.save(record);
						}
					}

				}
				
			} catch (Exception ex) {
				TaskLogger.getLogger().error("[taskResultNotify-"+index+"]["+taskName+"] update task object result error ,cause:" + ex.getMessage(),ex);
			}
	
			String _sql = "select CALL_RESULT from  "+  TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ") + " where OBJ_ID = ?  ";
			String  _callResult = easyQuery.queryForString(_sql, new Object[]{objId});
			
			//如果已经是成功接通过的名单数据则不再更新名单的接通状态
			if(!"0".equals(_callResult)){
				_callResult =  callResult+"";
			}
			
			//1  1  3 99 0
			TaskLogger.getLogger().info("[taskResultNotify-"+index+"]["+taskName+"] update taskObj["+objId+"], userData.curCallIndex="+userData.getString("curCallIndex")+",curCallIndex="+curCallIndex+",maxCallIndex="+maxCallIndex+",callResult="+callResult+"maxFailCount="+task.getIntValue("FAIL_CALL_COUNT"));
			int updateCount = 0;
			int interval = task.getIntValue("CALL_INTERVAL");
			int maxFailCount = task.getIntValue("FAIL_CALL_COUNT") ;  //这里原来是-1的，应该不需要减
			
			boolean recallFlag = false;
			if(Constants.isRecall()){  // 120 互损   16,  //预测试外呼, 坐席振铃超时   17,  //预测试外呼, 坐席挂断  18,  //预测试外呼, 用户挂断
				recallFlag = (callResult == 120||callResult == 16||callResult == 17||callResult == 18);
			}
			
			//如果呼叫结果0正常  120 互损 则不进行重呼处理，互损主要原因是：转坐席失败。  fix by tzc , 20220627
			if(callResult == 0  || recallFlag){
				String sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ") + " set TASK_STATE = 9 , CALL_RESULT = ? , SALE_FINISH_TIME = ?  where OBJ_ID = ?  ";
				int count = easyQuery.executeUpdate(sql, new Object[]{callResult,cal.getDateTime("-"),objId});
			}else{
				if(curCallIndex == 0){  //curCallIndex = 0,代表当前记录的号码已经外呼完成。
					if(callResult > 0){ 
						
						//如果呼叫次数少于配置的次数（CALL_TIMES < ?）
						String sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ") + " set TASK_STATE = 0 , CALL_RESULT = ?  , CALL_TIMES = CALL_TIMES+1, NEXT_RUN_TIME = ? where OBJ_ID = ?  and AGENT_FLAG = 0 and CALL_TIMES < ? ";
						updateCount = easyQuery.executeUpdate(sql, new Object[]{_callResult,(System.currentTimeMillis()/1000+interval),objId,maxFailCount});
						if(updateCount == 0){
							sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ") + " set TASK_STATE = 9 , CALL_TIMES = CALL_TIMES+1,CALL_RESULT = ?  , SALE_FINISH_TIME = ?  where OBJ_ID = ?  ";
							int count =  easyQuery.executeUpdate(sql, new Object[]{_callResult,cal.getDateTime("-"),objId});
						}
					}else{
						String sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ") + " set TASK_STATE = 9 , CALL_TIMES = CALL_TIMES+1,CALL_RESULT = ?  , SALE_FINISH_TIME = ?  where OBJ_ID = ?  ";
						int count =  easyQuery.executeUpdate(sql, new Object[]{_callResult,cal.getDateTime("-"),objId});
					}
				}else{
					String sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ") + " set TASK_STATE = 0 ,CALL_IDX = ? , NEXT_RUN_TIME = ?  where OBJ_ID = ?  ";
					int count =  easyQuery.executeUpdate(sql, new Object[]{curCallIndex,(System.currentTimeMillis()/1000+interval),objId});
				}
			}
			

			//呼叫结果为UserPowerOff = 7  UnallocateNumber = 5 把号码保存到平台的呼叫异常号码表中。
			if(callResult == 5 || callResult == 7){ 
				this.saveExceptionPhone(callResult, called);
			}
			
		} catch (SQLException ex) {
			TaskLogger.getLogger().error("[taskResultNotify-"+index+"]["+taskName+"] 处理任务结果通知失败,cause:"+ex.getMessage(),ex);
			throw new MessageException(ex.getMessage());
		}finally{
			this.taskResultNotifyExt(robotSerialId,taskResult);
		}
	}
	
	
	private void taskResultNotifyExt(String serialId,JSONObject taskResult){
		EventDispatcher.addEvent(new NotifyExecutor("STAT-TASK-RESULT-", serialId, taskResult));
	}
	

	/**
	 * 每天重呼数据，另外写入历史表
	 * @param taskId
	 * @param entId
	 * @param serialId
	 * @param record
	 */
	private void evedayTask(String taskId,String entId,String serialId, EasyRecord record){
		//呼叫历史
		if(TaskContext.getContext(taskId, entId).isEvedayCall()){//每天重呼数据
			EasyQuery query = QueryFactory.getWriteQuery(entId);
			EntContext context = EntContext.getContext(entId);
			try {
				//cly20231208 支持根据任务分表
				if(StringUtils.isNotBlank(record.getString("STD_CLASS")))
					query.execute("delete from "+TaskObjTableUtils.getObjRobotHisTableName(taskId,context.getSchemaId())+" where OBJ_ID = ? and DATE_ID = ?", new Object[]{record.getString("OBJ_ID"),record.getString("DATE_ID")});

				EasyRecord easyRecord = new EasyRecord(TaskObjTableUtils.getObjRobotHisTableName(taskId,context.getSchemaId()),"CALL_OBJ_ID");
				easyRecord.setColumns(record);
				easyRecord.setPrimaryValues(serialId);
				query.save(easyRecord);
			} catch (Exception ex) {
				TaskLogger.getLogger().error(ex.getMessage(),ex);
			}
		}
	}
	
	
	private boolean isBlankDateTime(String dateTime){
		if(StringUtils.isBlank(dateTime)) return true;
		if(dateTime.startsWith("1970")) return true;
		return false;
	}
	
	
	private void  saveExceptionPhone(int callResult,String phoneNum){
		
	}
	
	
	private String getDateTime(String dateTime,String defaultDateTime){
		if(StringUtils.isBlank(dateTime)){
			return defaultDateTime;
		}
		if(dateTime.startsWith("1970")) return defaultDateTime;
		return dateTime;
	}
	
	/**
	 * 
	 * @param createTime
	 * @return
	 */
	private String getCreateTime(String createTime){
		
		if(StringUtils.isBlank(createTime)){
			EasyCalendar cal = EasyCalendar.newInstance();
			return cal.getDateString("-");
		}
		if(createTime.startsWith("1")){
			EasyCalendar cal = EasyCalendar.newInstance();
			return cal.getDateString("-");
		}
		return createTime;
	}
	

	
	/**
	 * 获得任务信息，从memcache中获取，获取不到再从数据库中获取
	 * @param entId
	 * @param taskId
	 * @return
	 * @throws MessageException
	 */
	private JSONObject getTask(String entId,String taskId) throws MessageException{  
		
		try {
			String taskObjeString = cache.get("TASK_"+taskId);
			//TaskResultNotifyLogger.getLogger().info(TASK_COMMAND+"taskId:"+taskId+",mq taskObjeString:"+taskObjeString);
			if(StringUtils.isNotBlank(taskObjeString)){
				return JSONObject.parseObject(taskObjeString);
			}
			String sql = "select * from "+EntContext.getContext(entId).getTableName( "CC_TASK")+"  where  TASK_ID = ? ";
			JSONObject taskObj = QueryFactory.getWriteQuery(entId).queryForRow(sql , new Object[]{taskId},new JSONMapperImpl());
			//TaskResultNotifyLogger.getLogger().info(TASK_COMMAND+"taskId:"+taskId+",select taskObj:"+taskObj);
			cache.put("TASK_"+taskId,taskObj.toJSONString(),300); //300秒更新一次缓存
			return taskObj;
		} catch (SQLException ex) {
			TaskLogger.getLogger().error("SdrHandleMessage.getTask() error  -> taskId:"+taskId+",cause:"+ex.getMessage(),ex);
			throw new MessageException(ex.getMessage());
		}
		
	}
	
	private JSONObject getTaskObject(String entId,String taskId,String objId) throws MessageException{  
		JSONObject taskObj =  new JSONObject();
		try {
			String sql = "select CUST_ID,CUST_NAME,BATCH_ID from "+TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")+"  where  OBJ_ID = ? ";
			taskObj = QueryFactory.getWriteQuery(entId).queryForRow(sql , new Object[]{objId},new JSONMapperImpl());
		
		} catch (SQLException ex) {
			TaskLogger.getLogger().error("SdrHandleMessage.getTaskObject() error  -> objId:"+objId+",cause:"+ex.getMessage(),ex);
			throw new MessageException(ex.getMessage());
		}
		return taskObj;
	}
	
}
