package com.yunqu.yc.api.servlet;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.concurrent.atomic.AtomicInteger;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.executor.EventDispatcher;
import com.yunqu.yc.api.executor.EventHandlerThread;
import com.yunqu.yc.api.executor.test.BlockingTestExecutor;
import com.yunqu.yc.api.executor.test.IdleTestExecutor;
import com.yunqu.yc.api.executor.test.TimeoutTestExecutor;
import com.yunqu.yc.api.log.TaskLogger;

/**
 * 线程池测试Servlet
 * 用于测试EventDispatcher和EventHandlerThread的各种边界情况
 * 
 * 测试URL示例：
 * - 启动超时测试: /threadPoolTest?action=timeout&count=10&sleepTime=6000
 * - 启动阻塞测试: /threadPoolTest?action=block&count=60&blockTime=300000
 * - 启动空闲测试: /threadPoolTest?action=idle&count=5
 * - 停止所有测试: /threadPoolTest?action=stop
 * - 查看状态: /threadPoolTest?action=status
 */
@WebServlet("/threadPoolTest")
public class ThreadPoolTestServlet extends HttpServlet {
    
    private static final long serialVersionUID = 1L;
    private static final AtomicInteger executorCounter = new AtomicInteger(0);
    private static volatile boolean testRunning = false;
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        this.doPost(request, response);
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        if(!Constants.eventThreadTest()){
            response.getWriter().println("not open test thread!");
            return;
        }
        if(request.getUserPrincipal()==null) {
            try {
                response.getWriter().println("request fail!");
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            return;
        }
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        String action = request.getParameter("action");
        
        try {
            out.println("<html><head><title>线程池测试</title></head><body>");
            out.println("<h1>EventDispatcher 线程池测试</h1>");
            out.println("<hr>");
            
            if ("timeout".equals(action)) {
                handleTimeoutTest(request, out);
            } else if ("block".equals(action)) {
                handleBlockTest(request, out);
            } else if ("idle".equals(action)) {
                handleIdleTest(request, out);
            } else if ("stop".equals(action)) {
                handleStopTest(out);
            } else if ("status".equals(action)) {
                handleStatusCheck(out);
            } else {
                showTestMenu(out);
            }
            
            out.println("<hr>");
            out.println("<p><a href='/"+Constants.APP_NAME+"/threadPoolTest?action=status'>查看当前状态</a> | ");
            out.println("<a href='/"+Constants.APP_NAME+"/threadPoolTest?action=stop'>停止所有测试</a> | ");
            out.println("<a href='/"+Constants.APP_NAME+"/threadPoolTest'>返回主菜单</a></p>");
            out.println("</body></html>");
            
        } catch (Exception e) {
            out.println("<p style='color:red'>测试执行出错: " + e.getMessage() + "</p>");
            TaskLogger.getLogger().error("ThreadPoolTestServlet 执行出错", e);
        }
    }
    
    /**
     * 显示测试菜单
     */
    private void showTestMenu(PrintWriter out) {
        out.println("<h2>测试菜单</h2>");
        out.println("<h3>1. 子线程执行超时测试（验证5秒告警机制）</h3>");
        out.println("<form method='post'>");
        out.println("  <input type='hidden' name='action' value='timeout'>");
        out.println("  执行器数量: <input type='number' name='count' value='5' min='1' max='100'>");
        out.println("  执行时间(毫秒): <input type='number' name='sleepTime' value='6000' min='1000' max='30000'>");
        out.println("  <input type='submit' value='启动超时测试'>");
        out.println("</form>");
        
        out.println("<h3>2. 线程池阻塞测试（验证无法获取空闲线程和无法创建新线程）</h3>");
        out.println("<form method='post'>");
        out.println("  <input type='hidden' name='action' value='block'>");
        out.println("  阻塞执行器数量: <input type='number' name='count' value='60' min='1' max='200'>");
        out.println("  阻塞时间(毫秒): <input type='number' name='blockTime' value='300000' min='10000' max='600000'>");
        out.println("  <input type='submit' value='启动阻塞测试'>");
        out.println("</form>");
        
        out.println("<h3>3. 子线程空闲超时测试（验证120秒超时退出）</h3>");
        out.println("<form method='post'>");
        out.println("  <input type='hidden' name='action' value='idle'>");
        out.println("  执行器数量: <input type='number' name='count' value='10' min='1' max='200'>");
        out.println("  每个执行器处理消息数量: <input type='number' name='msgCount' value='10' min='1' max='1000'>");
        out.println("  <input type='submit' value='启动空闲测试'>");
        out.println("</form>");
        
        out.println("<h3>当前线程池配置</h3>");
        out.println("<p>最大线程数: <strong>" + Constants.getEventThreadCount() + "</strong></p>");
        out.println("<p>空闲超时时间: <strong>"+ EventHandlerThread.IDLE_TIMEOUT +"ms</strong></p>");
        out.println("<p>执行超时告警阈值: <strong>"+EventHandlerThread.EXECUTION_WARN_TIMEOUT+"ms</strong></p>");
    }

    /**
     * 处理超时测试
     */
    private void handleTimeoutTest(HttpServletRequest request, PrintWriter out) {
        int count = Integer.parseInt(request.getParameter("count"));
        long sleepTime = Long.parseLong(request.getParameter("sleepTime"));

        out.println("<h2>子线程执行超时测试</h2>");
        out.println("<p>启动 " + count + " 个执行器，每个执行时间: " + sleepTime + "ms</p>");

        if (sleepTime > 5000) {
            out.println("<p style='color:orange'>⚠️ 执行时间超过5秒，将触发超时告警</p>");
        }

        testRunning = true;

        for (int i = 0; i < count; i++) {
            if (!testRunning){
                break;
            }
            int executorId = executorCounter.incrementAndGet();
            String sessionId = "timeout-test-" + executorId;

            TimeoutTestExecutor executor = new TimeoutTestExecutor(
                sessionId, sleepTime, "timeout", executorId);

            EventDispatcher.addEvent(executor);
        }
        out.println("<p>✓ 已提交执行器： " + count + "个/p>");
        out.println("<p style='color:green'><strong>测试已启动！请查看日志观察超时告警信息。</strong></p>");
        out.println("<p>预期结果：如果执行时间 > 5秒，将在日志中看到 [WARN] 超时告警信息</p>");
    }

    /**
     * 处理阻塞测试
     */
    private void handleBlockTest(HttpServletRequest request, PrintWriter out) {
        int count = Integer.parseInt(request.getParameter("count"));
        long blockTime = Long.parseLong(request.getParameter("blockTime"));

        out.println("<h2>线程池阻塞测试</h2>");
        out.println("<p>启动 " + count + " 个阻塞执行器，每个阻塞时间: " + (blockTime/1000) + "秒</p>");

        int maxThreads = Constants.getEventThreadCount();
        if (count >= maxThreads) {
            out.println("<p style='color:red'>⚠️ 执行器数量(" + count + ") >= 最大线程数(" + maxThreads + ")，将耗尽线程池</p>");
            out.println("<p>预期结果：</p>");
            out.println("<ul>");
            out.println("<li>前 " + maxThreads + " 个执行器将占用所有线程</li>");
            out.println("<li>后续执行器将在消息队列中等待</li>");
            out.println("<li>主线程将无法获取空闲线程</li>");
            out.println("<li>主线程将无法创建新线程（已达上限）</li>");
            out.println("</ul>");
        }

        testRunning = true;
        BlockingTestExecutor.setStopFlag(false); // 重置停止标志

        for (int i = 0; i < count; i++) {
            if (!testRunning){
                break;
            }
            int executorId = executorCounter.incrementAndGet();
            String sessionId = "block-test-" + executorId;

            BlockingTestExecutor executor = new BlockingTestExecutor(
                sessionId, blockTime, executorId);

            EventDispatcher.addEvent(executor);
        }

        out.println("<p>✓ 已提交阻塞执行器：" + count + "个</p>");
        out.println("<p style='color:green'><strong>阻塞测试已启动！</strong></p>");
        out.println("<p style='color:blue'>提示：可以通过\"停止所有测试\"来提前结束阻塞</p>");
    }

    /**
     * 处理空闲测试
     */
    private void handleIdleTest(HttpServletRequest request, PrintWriter out) {
        int count = Integer.parseInt(request.getParameter("count"));
        int msgCount = Integer.parseInt(request.getParameter("msgCount"));

        out.println("<h2>子线程空闲超时测试</h2>");
        out.println("<p>启动 " + count + " 个快速执行器，每个执行器执行"+msgCount+"个消息，执行完成后线程将进入空闲状态</p>");
        out.println("<p>预期结果：线程在空闲120秒后将自动退出</p>");

        testRunning = true;

        for (int i = 0; i < count; i++) {
            if (!testRunning){
                break;
            }
            String sessionId = "idle-test-" + i;
            for (int j = 0; j < msgCount; j++) {
                if (!testRunning){
                    break;
                }
                int executorId = executorCounter.incrementAndGet();
                // 快速执行，执行时间100ms
                IdleTestExecutor executor = new IdleTestExecutor(sessionId, executorId, 100);
                EventDispatcher.addEvent(executor);
            }
        }
        out.println("<p>✓ 已提交空闲测试执行器：" + count + "，每个执行器执行"+msgCount+"个消息</p>");
        out.println("<p style='color:green'><strong>空闲测试已启动！</strong></p>");
        out.println("<p>请观察日志，约120秒后应该看到线程退出信息</p>");
    }

    /**
     * 停止所有测试
     */
    private void handleStopTest(PrintWriter out) {
        out.println("<h2>停止所有测试</h2>");

        if (!testRunning) {
            out.println("<p style='color:orange'>当前没有运行中的测试</p>");
            return;
        }

        // 设置阻塞执行器的停止标志
        BlockingTestExecutor.setStopFlag(true);
        testRunning = false;

        out.println("<p style='color:green'>✓ 已发送停止信号给所有阻塞执行器</p>");
        out.println("<p>注意：已经在执行中的超时测试和空闲测试无法立即停止</p>");

        TaskLogger.getLogger().info("[TEST] 用户请求停止所有测试");
    }

    /**
     * 处理状态检查
     */
    private void handleStatusCheck(PrintWriter out) {
        out.println("<h2>线程池当前状态</h2>");

        // 获取线程池状态
        String threadPoolStatus = EventDispatcher.getThreadPoolStatus();
        out.println("<p><strong>线程池状态：</strong>" + threadPoolStatus + "</p>");

        // 显示配置信息
        out.println("<h3>配置信息</h3>");
        out.println("<ul>");
        out.println("<li>最大线程数: " + Constants.getEventThreadCount() + "</li>");
        out.println("<li>空闲超时时间: "+ EventHandlerThread.IDLE_TIMEOUT +"ms</li>");
        out.println("<li>执行超时告警阈值: "+EventHandlerThread.EXECUTION_WARN_TIMEOUT+"ms</li>");
        out.println("<li>线程清理间隔: "+EventDispatcher.CLEANUP_INTERVAL+"ms</li>");
        out.println("</ul>");

        // 显示测试状态
        out.println("<h3>测试状态</h3>");
        out.println("<p>测试运行状态: " + (testRunning ? "<span style='color:green'>运行中</span>" : "<span style='color:gray'>已停止</span>") + "</p>");
        out.println("<p>已创建执行器总数: " + executorCounter.get() + "</p>");

        // 显示使用说明
        out.println("<h3>测试说明</h3>");
        out.println("<ul>");
        out.println("<li><strong>超时测试</strong>: 验证执行时间超过5秒时的告警机制</li>");
        out.println("<li><strong>阻塞测试</strong>: 验证线程池耗尽时主线程的处理逻辑</li>");
        out.println("<li><strong>空闲测试</strong>: 验证线程空闲120秒后的自动退出机制</li>");
        out.println("</ul>");

        out.println("<p style='color:blue'>💡 建议在测试过程中观察应用日志，查看详细的执行信息和告警</p>");
    }
}
