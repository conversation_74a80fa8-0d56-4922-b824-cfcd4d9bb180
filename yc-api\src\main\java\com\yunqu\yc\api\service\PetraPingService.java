package com.yunqu.yc.api.service;

import java.util.List;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.base.Model;
import com.yunqu.yc.api.context.EntContext;
import com.yunqu.yc.api.listener.GlobalContextListener;
import com.yunqu.yc.api.log.ApiLogger;
import com.yunqu.yc.api.log.DBLogger;
import com.yunqu.yc.api.model.RequestDataModel;
import com.yunqu.yc.api.mqclient.MQClient;

/**
 * 同步接口
 * <AUTHOR>
 *
 */
public class PetraPingService implements Runnable {


	private static EasyQuery readQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.READ_DS_NAME);
	
	private static int TIMER = 60*1000;  //1分钟PING一次
	
	private static String COMMAND = "bizStatusPing";
	
	private static  long petraPingTime = System.currentTimeMillis();
	
	private static  int pingCount = 0;
	
	private   int timer = 5*60*1000;

	
	public void run() {
		
		AppContext appContext = AppContext.getContext(Constants.APP_NAME);
		int count = 0;

		while (GlobalContextListener.runState) {
			count++;
			if(count < 60){//1分钟PING一次
				try {
					Thread.sleep(1000);
					continue;
				} catch (Exception ex) {
					return;
				}
			}

			count = 0;

			//获取是否是磐石接口服务，否则不做任务的处理，只有是为true的时候才执行通知服务。
			String serverFlag = appContext.getProperty("SERVER_FLAG", "false");
			if("false".equalsIgnoreCase(serverFlag)){  //如果不是配置了服务，则不做处理。
				try {
					Thread.sleep(5000);
					continue;
				} catch (Exception ex) {
					return;
				}
			}
			
			if(System.currentTimeMillis()-petraPingTime>timer){
				petraPingTime = System.currentTimeMillis();
				DBLogger.info("","YC-bizStatusPing", "YC-bizStatusPing", "-", "5分钟定时监控,心跳检查,期间执行次数:"+pingCount,  "-");
				pingCount=0;
			}
			
			String sql = "select ENT_ID,ENT_STATE  from CC_ENT  where  ENT_TYPE <> 2 ";
			try {
				readQuery.setMaxRow(20000);
				List<EasyRow> rows = readQuery.queryForList(sql, new Object[]{});
				for(EasyRow row:rows){
					if(!GlobalContextListener.runState) break;
					RequestDataModel model = Model.newRequestDataModel(COMMAND);
					JSONObject  data = new JSONObject();
					data.put("entId", row.getColumnValue("ENT_ID"));
					data.put("state", row.getColumnValue("ENT_STATE"));
					data.put("bizCallBackUrl", EntContext.getContext(row.getColumnValue("ENT_ID")).getMarsGwUrl());
					model.setData(data.toJSONString());
					try {
						MQClient.sendPetraGw(model.toString());
						pingCount++;
					} catch (Exception ex) {
						ApiLogger.getLogger().error(ex.getMessage()+"，企业信息："+model.toString());
					}
				}
			} catch (Exception ex) {
				DBLogger.error("","YC-bizStatusPing", "YC-bizStatusPing", "","心跳检查异常",  "cause:"+ex.getMessage());
				ApiLogger.getLogger().error(ex.getMessage() ,ex);
				try {
					Thread.sleep(5000);
				} catch (Exception e) {
					return;
				}
			}
		}
	}


}