package com.yunqu.yc.portal.excel.export;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.excel.BaseRowHandler;
import com.yunqu.yc.portal.service.SqlService;
import com.yunqu.yc.portal.utils.PhoneCryptor;
import com.yunqu.yc.portal.utils.TempFiledUtils;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;

public class TaskRobotResultExportHandler extends BaseRowHandler {

    private final JSONObject params;
    private final YCUserPrincipal userPrincipal;
    private final Map<String,String> dataTypeMap = new HashMap<>();
    private boolean num = false;
    private final List<String> headers = new ArrayList<>();
    private final List<String> columns = new ArrayList<>();

    public TaskRobotResultExportHandler(YCUserPrincipal userPrincipal,JSONObject params) {
        this.params = params;
        this.userPrincipal = userPrincipal;
    }

    @Override
    protected void begin() {
        super.begin();
        getColumns();
    }

    @Override
    public String getFileName() {
        return "机器人任务列表";
    }

    @Override
    public String getTableName(String tableName) {
        return userPrincipal.getSchemaName()+"."+tableName;
    }

    @Override
    public List<List<String>> getHead() {
        List<List<String>> header = new ArrayList<>();
        header.add(Arrays.asList("客户名称"));
        header.add(Arrays.asList("客户电话"));
        header.add(Arrays.asList("客户意向"));
        header.add(Arrays.asList("分值"));
        header.add(Arrays.asList("标签"));
        header.add(Arrays.asList("拨打结果"));
        header.add(Arrays.asList("拨打时间"));
        header.add(Arrays.asList("通话时长"));
        header.add(Arrays.asList("外呼次数"));
        header.add(Arrays.asList("外呼成功次数"));
        header.add(Arrays.asList("所属任务"));

        //客户资料模板字段
        for (String key : headers) {
            header.add(Arrays.asList(key));
        }

        return header;
    }

    @Override
    public List<String> parseData(Map<String, String> data) {
        data = PhoneCryptor.getInstance().decryptMap(data,new String[]{"TEL_NUM1"},false);

        List<String> list = new ArrayList<>();
        list.add(changeName(data.get("CUST_NAME")));
        list.add(changeNum(data.get("CUST_PHONE")));
        list.add(data.get("STD_CLASS"));
        list.add(data.get("SCORE"));
        list.add(getLabels(data.get("LABELS")));
        list.add(getClearCause(data.get("CLEAR_CAUSE")));
        list.add(data.get("BEGIN_TIME"));
        list.add(data.get("CALL_TIME")+"秒");
        list.add(data.get("CALL_TIMES"));
        list.add(data.get("SUCC_CALL_TIMES"));
        list.add(data.get("TASK_NAME"));

        for (String key : columns) {
            list.add(data.get(key));
        }

        return list;
    }

    private String getClearCause(String val) {
        String text = "";
        switch (val) {
            case "0":
                text="成功";
                break;
            case "1":
                text="无人应答";
                break;
            case "2":
                text="用户忙";
                break;
            case "3":
                text="用户挂机";
                break;
            case "4":
                text="网络忙";
                break;
            case "5":
                text="空号";
                break;
            case "6":
                text="拒接";
                break;
            case "7":
                text="关机";
                break;
            case "8":
                text="停机";
                break;
            case "9":
                text="不在服务区";
                break;
            case "10":
                text="传真机";
                break;
            case "11":
                text="欠费";
                break;
            case "12":
                text="重复号码";
                break;
            case "13":
                text="电话总机";
                break;
            case "14":
                text="久叫不应";
                break;
            case "98":
                text="坐席挂机";
                break;
            case "99":
                text="系统错误";
                break;
            case "100":
                text="其它呼叫失败";
                break;
            default:
                break;
        }
        return text;
    }

    private String getLabels(String labels) {
        if (StringUtils.isBlank(labels)){
            return labels;
        }
        JSONArray labelsArray = JSONArray.parseArray(labels);
        if (labelsArray != null && !labelsArray.isEmpty()){
            //用逗号分割
            return StringUtils.join(labelsArray, ",");
        }
        return null;
    }

    private String changeNum(String phone) {
        if (num){
            return advReplace(phone,3,5,"*");
        }
        return phone;
    }

    private String changeName(String custName) {
        if (StringUtils.isBlank(custName)){
            return null;
        }
        String dataType = dataTypeMap.get("CUST_NAME");
        if (StringUtils.isBlank(dataType)){
            return custName;
        }
        if ("userName".equals(dataType)){
            return advReplace(custName,1,3,"*");
        }
        if ("hide".equals(dataType)){
            return "--";
        }
        return custName;
    }

    @Override
    public EasySQL getSql() {
        SqlService sqlService = new SqlService(userPrincipal);
        return sqlService.getRobotObjResullSql(params);
    }

    private void getColumns() {
        try {
            Map<String, String> tempMap = this.getQuery().queryForRow("select " + TempFiledUtils.fileds(",", userPrincipal.getSchemaName()) + " from " + getTableName("cc_cust_temp") + " where TEMP_ID = ?", new Object[]{params.getString("tempId")}, new MapRowMapperImpl());
            for (String key : tempMap.keySet()) {
                String jsonStr = tempMap.get(key);
                if (StringUtils.notBlank(jsonStr)) {
                    JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                    String inuse = jsonObject.getString("inuse");
                    if ("1".equals(inuse)) {
                        String dataType = jsonObject.getString("dataType");

                        //排除客户姓名和客户号码的字段
                        if (!key.startsWith("CUST_NAME") && !key.startsWith("TEL_NUM")){
                            headers.add(jsonObject.getString("name"));
                            columns.add(key);
                        }

                        dataTypeMap.put(key, dataType);
                        if (key.startsWith("TEL_NUM") && "phone".equals(dataType)){
                            num = true;
                        }

                    }
                }
            }
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
        }
    }

    private String advReplace(String text, int start, int length, String placeText) {
        if (text == null) return "";
        placeText = placeText != null ? placeText : "*";

        if (Math.abs(start) > text.length()) return text;

        if (start > 0) {
            String textArr1 = text.substring(0, start);
            int end = start + length;
            boolean longText = end > text.length();
            String textArr2 = longText ? "" : text.substring(end);
            int replaceArrLength = longText ? text.length() - start : length;
            return textArr1 + repeat(placeText, replaceArrLength) + textArr2;
        } else {
            String end = text.substring(text.length() + start);
            int startlen = text.length() + start - length;
            int replacelen = startlen > 0 ? length : length + startlen;
            int startIndex = Math.max(startlen, 0);
            String startText = text.substring(0, startIndex);
            return startText + repeat(placeText, replacelen) + end;
        }
    }

    private String repeat(String str, int times) {
        return new String(new char[times]).replace("\0", str);
    }

}
