<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>通话记录查询</title>
	<style>
		.layer_notice {
			padding: 10px;
			height: 75px;
			width: 330px;
			display: none;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form class="form-inline" name="searchForm" id="searchForm" >
		<div class="ibox">
			<div class="ibox-title clearfix">
 				<div class="form-group">
 						<h5><span class="glyphicon glyphicon-earphone"></span><span data-i18n="通话记录查询">通话记录查询</span> </h5>
						<div class="input-group input-group-sm pull-right btn-group">
							<%-- <EasyTag:res resId="yc_call_report_cdr_exp"> --%>
								<a class="btn btn-sm btn-success"  href="javascript:void(0)" onclick="Report.exportEntRepotList()"><i class="glyphicon glyphicon-export"></i> <span data-i18n="导出"> 导 出</span></a>
				   			<%-- </EasyTag:res> --%>
				   			<EasyTag:res resId="yc_call_report_cdr_down">
				   				<a class="btn btn-sm btn-info" href="javascript:void(0)" onclick="Report.exportRepotFiles()"><i class="glyphicon glyphicon-import"></i> <span data-i18n="页面录音下载">下载录音 </span> </a>
				   				<a class="btn btn-sm btn-default hide"  href="javascript:void(0)" onclick="Report.exportRepotAllFiles()"><i class="glyphicon glyphicon-import"></i> <span data-i18n="范围录音下载">范围录音下载 </span> </a>
				   			</EasyTag:res>
				   			<EasyTag:res resId="BTN_DATA_VERIFY">
								<button type="button" class="btn btn-sm btn-info btn-outline btn-verify" onclick="verifyData('CC_CALL_RECORD')"><span data-i18n="完整性校验" title="数据防止篡改，采用一种校验策略">完整性校验</span></button>
							</EasyTag:res>
				    	</div>
			    </div>
			    <hr style="margin: 5px -15px">
				<div class="form-group">
					<input type="hidden" name="source" id="source" value="${param.source}">
					<div class="input-group input-group-sm">
						<span class="input-group-addon" data-i18n="呼叫方向">呼叫方向</span> 
               			<select name="callType" id="callType" class="form-control" multiple="multiple">
							<option value="outbound" data-i18n="呼出">呼出</option>
							<option value="inbound" data-i18n="呼入">呼入</option>
							<option value="agentIn" data-i18n="席间呼入">席间呼入</option>
							<option value="agentOut" data-i18n="席间呼出">席间呼出</option>
							<option value="consultIn" data-i18n="咨询呼入">咨询呼入</option>
							<option value="transferIn" data-i18n="转移呼入">转移呼入</option>
							<option value="transferOut" data-i18n="呼转外线">呼转外线</option>
							<option value="other" data-i18n="其他">其他</option>
						</select>
				    </div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" data-i18n="开始时间">开始时间</span> 
						<input  name="startTime" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})" id="startTime" data-mars-reload="false" id="startDate" data-mars-top="true"  data-mars="common.todayFirstTime"  class="form-control input-sm Wdate">
						<span class="input-group-addon">-</span>
						<input  name="endTime" onClick="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})" id="endTime" data-mars-reload="false"  data-mars-top="true"  data-mars="common.todayEndTime" class="form-control input-sm Wdate">
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon">
						<select style="border: none;background-color: #fafafa;height: 18px;cursor: pointer;" id="callFlag" name="callFlag" >
						<option value="1" data-i18n="主叫号码">主叫号码</option>
						<option value="2" data-i18n="被叫号码">被叫号码</option>
             		    </select>
             		    </span> 
						<input id="callPhone" size = "11"  class="form-control" name="callPhone">	
					</div>
					<div class="input-group input-group-sm adminShow">
						<span class="input-group-addon" data-i18n="技能组">技能组</span> 
						<select name="groupId" class="form-control" data-mars="common.skillDictByRole" data-mars-top="true" onchange="Report.onChangeSkill()">
							<option value="" data-i18n="请选择">请选择</option>
						</select>
					</div>
 					<div class="input-group input-group-sm for-hiddenSys">
						<span class="input-group-addon" data-i18n="坐席工号">坐席工号</span> 
						<select name="agentId" id="agentId" data-mars="common.userDict" size="1" data-mars-top="true" multiple="multiple">
             		    </select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" data-i18n="通话时长">通话时长(s)</span> 
						<input id="startBillTime" name="startBillTime" size="2" class="form-control" autocomplete="off" type="number" onkeypress="return event.charCode >= 48" min="0" max="60" oninput="if (value < 0) value = 0;if(value>36000)value=36000"> 
						<span class="input-group-addon">-</span>
						<input id="endBillTime" name="endBillTime" size="2" class="form-control" autocomplete="off" type="number" onkeypress="return event.charCode >= 48" min="0" max="60" oninput="if (value < 0) value = 0;if(value>36000)value=36000">
						<span class="input-group-addon">-</span> 
						<select name="billTime" class="form-control">
							<option value="0" data-i18n="全部">全部</option>
							<option value="1" data-i18n="有效通话">有效通话</option>
							<option value="2" data-i18n="0~30秒">0~30秒</option>
							<option value="3" data-i18n="30秒~1分钟">30秒~1分钟</option>
							<option value="4" data-i18n="1分钟~2分钟">1分钟~2分钟</option>
							<option value="5" data-i18n="2分钟~5分钟">2分钟~5分钟</option>
							<option value="6" data-i18n="5分钟以上">5分钟以上</option>
						</select>
						
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" data-i18n="呼叫结果">呼叫结果</span> 
						<select name="clearCause" id="clearCause" class="form-control" data-mars="report.clearCauseDict" data-mars-top="true">
							<option value="" data-i18n="请选择">请选择</option>
						</select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" data-i18n="挂机类型">挂机类型</span> 
						<select name="agentRelease" id="agentRelease" class="form-control">
							<option value="" data-i18n="请选择">请选择</option>
							<option value="1" data-i18n="用户挂断">用户挂断</option>
							<option value="2" data-i18n="坐席挂断">坐席挂断</option>
							<option value="3" data-i18n="系统挂断">系统挂断</option>
							<option value="4" data-i18n="监听挂断">监听挂断</option>
							<option value="5" data-i18n="转移挂断">转移挂断</option>
							<option value="6" data-i18n="强拆挂断">强拆挂断</option>
							<option value="7" data-i18n="机器人挂断">机器人挂断</option>
							<option value="8" data-i18n="机器人转移挂断">机器人转移挂断</option>
							<option value="9" data-i18n="转满意度挂断">转满意度挂断</option>
							<option value="10" data-i18n="排队超时挂断">排队超时挂断</option>
						</select>
					</div>
					<div class="input-group input-group-sm">
						<span class="input-group-addon" data-i18n="满意度">满意度</span> 
						<select name="satisfId" id="satisfId" class="form-control">
							<option value="" data-i18n="请选择">请选择</option>
							<option value="0" data-i18n="未评价">未评价</option>
							<option value="1" data-i18n="非常满意">非常满意</option>
							<option value="2" data-i18n="满意">满意</option>
							<option value="3" data-i18n="一般">一般</option>
							<option value="4" data-class="label label-warning" data-i18n="不满意">不满意</option>
							<option value="5" data-class="label label-warning" data-i18n="处理结果不满意">处理结果不满意</option>
						</select>
					</div>
					<div class="input-group input-group-sm hide hidden">
						<span class="input-group-addon" data-i18n="地区">地区</span> 
						<select name="areaCode" id="areaCode" class="form-control" data-mars="common.areaDict">
							<option value="" data-i18n="请选择">请选择</option>
						</select>
					</div>
					<div class="input-group input-group-sm">
					
						<button type="button" class="btn btn-sm btn-default" onclick="Report.loadData()"><span class="glyphicon glyphicon-search"></span> <span data-i18n="搜索">搜索</span></button>
				   	</div>
 				</div>

			</div>
			<div class="ibox-content">
				<table  data-mars="report.list" class="table table-auto table-striped table-bordered table-hover table-condensed text-c" data-auto-fill="10">
					<thead>
						<tr>
							<th data-i18n="编号">编号</th>
							<th data-i18n="日期" class="text-c">日期</th>
							<th data-i18n="开始时间" class="text-c">开始时间</th>
							<th data-i18n="结束时间" class="text-c">结束时间</th>
							<th data-i18n="通话时长" class="text-c">通话时长</th>
							<th data-i18n="计费时长" class="text-c" title="单位：分钟">计费时长</th>
							<th data-i18n="坐席工号" class="text-l">坐席工号</th>
							<th data-i18n="话机号码" class="text-c" nowrap="nowrap">话机号码</th>
							<th data-i18n="主叫号码" class="text-l">主叫</th>
							<th data-i18n="被叫号码" class="text-l for-hiddenSys">被叫</th>
							<th data-i18n="号码归属地" class="text-l">号码归属地</th>
							<th data-i18n="技能组" class="text-l">技能组</th>
							<th data-i18n="呼叫方向" class="text-c">呼叫方向</th>
							<th data-i18n="呼叫结果" class="text-l">呼叫结果</th>
							<th data-i18n="挂机类型" class="text-l">挂机类型</th>
							<th data-i18n="满意度" class="text-l">满意度</th>
							<!-- <th data-i18n="质检" class="text-c for-hiddenSys">质检</th> -->
							<th data-i18n="操作" class="text-c">操作</th>
						</tr>
					</thead>
					<tbody id="dataList"></tbody>
				</table>
				 <div class="row paginate">
				 	 <jsp:include page="/pages/common/pagination.jsp"/>
				 	<%-- 
					<c:choose>
					      <c:when test="${param.allExport == 'true'}">
					         <jsp:include page="/pages/common/pagination.jsp"/>
					      </c:when>
					      <c:otherwise>
					          <jsp:include page="/pages/common/pagination_more.jsp"/>
					      </c:otherwise>
					</c:choose>  --%>
	            </div> 
			</div>
		</div>
	</form>
					<script id="list-template" type="text/x-jsrender">
					 {{for list}}
							<tr>
							<td data-verify="{{:SERIAL_ID}}">{{:#index+1}}</td>
							<td>{{:DATE_ID}}</td>
							<td>{{dateToTime:BILL_BEGIN_TIME}}</td>
							<td>{{dateToTime:BILL_END_TIME}}</td>
							<td>{{clock:BILL_TIME}} </td>
							<td>{{:FEE_TIME_60}}</td>
							<td class="text-l">{{call: fn='fnAgentInfo'}}</td>
							<td nowrap="nowrap">{{:PHONE_NUM}}</td>
							<td class="text-l">{{call:CALLER _CALLER fn='getPhone'}}</td>
							<td class="text-l for-hiddenSys">{{call:CALLED _CALLED fn='getPhone'}}</td>
							<td class="text-l">{{getText:AREA_CODE 'areaCode'}}</td>
							<td class="text-l">{{getText:GROUP_ID 'groupId'}} </td>
							<td>{{call:CREATE_CAUSE fn='createCause'}}</td>
							<td class="text-l">{{getText:CLEAR_CAUSE '#clearCause'}} </td>
							<td class="text-l">{{getText:AGENT_RELEASE '#agentRelease'}} </td>
							<td class="text-l">{{if SATISF_ID > 0}} {{getText:SATISF_ID '#satisfId'}} {{else}}<span data-i18n="未评价">未评价</span>{{/if}} </td>
							<!-- <td class="for-hiddenSys">{{if QC_STATE == 1}}<span data-i18n="已质检">已质检</span>{{else}}<span data-i18n="未质检">未质检</span>{{/if}}</td> -->
							<td>
								{{if VIDEO_FILE}}
                                    <a href="javascript:void(0)" onclick="Report.videoListener('{{:SERIAL_ID}}','{{:BILL_TIME}}')">播放视频</a>
                                {{/if}}
                                {{if RECORD_FILE}}<input type="hidden" name="files" value="{{:SERIAL_ID}}">
                                    <a href="javascript:void(0)" onclick="Report.recoredListener('{{:SERIAL_ID}}')"><span data-i18n="在线播放">在线播放</span></a>
									{{call: fn='fnIvrPaly'}}
                                {{/if}}
                            	{{if ORDER_ID}}
									{{call: fn='showOrderInfo'}}
                            	{{/if}}
                            </td>
							</tr>
						{{/for}}
					 </script>
</EasyTag:override>

<EasyTag:override name="script">

<script type="text/javascript" src="${ctxPathPrefix}/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<script type="text/javascript">

		jQuery.namespace("Report");

		var ivrPaly = getEntExtConfig('IVR_PALY');//企业拓展配置，播放ivr音乐，1表示需要

		var checkSync;//计时器

		loadPageI18n();
		$(function(){
			
			$("#searchForm").render({success:function(result){
				if(result['report.list']){
					loadPageI18n();
				}
				requreLib.setplugs('multiselect',function(){
					$("#agentId").multiselect({
						 enableFiltering: true,
						 maxHeight: 200,
						 includeSelectAllOption: true,
						 selectAllText:getI18nText('全选'),
						 nonSelectedText: getI18nText('请选择')
					});
					$("#callType").multiselect({
						enableFiltering: true,
						maxHeight: 200,
						includeSelectAllOption: true,
						selectAllText:getI18nText('全选'),
						nonSelectedText: getI18nText('请选择')
					});
				});
			}});
			if('${userData.roleType}'=='1'){
				$(".adminShow").show();
			}else{
				$(".adminShow").hide();
			}

			$("form").after("<div class='layer_notice'></div>");
		});
		Report.loadData=function(){
			var data = form.getJSONObject("#searchForm");

			if(data["callFlag"]==2&&data["callPhone"]&&data["callPhone"]!=''){
				$("#searchForm").searchData({success:function(result){
					loadPageI18n();
				}});
				return;
			}

			if(!checkDate(data,"startTime","endTime",31)){
				return;
			}

			$("#searchForm").searchData({success:function(result){
				if(result['report.list']){
					loadPageI18n();
				}
			}});
		}
		Report.onChangeSkill = function(){
			var val = $("select[name='groupId']").val();
			$("#agentId").render({data:{groupId:val},success:function(){
				requreLib.setplugs('multiselect',function(){
					$("#agentId").multiselect("destroy").multiselect({
						 enableFiltering: true,
						 maxHeight: 200,
						 includeSelectAllOption: true,
						 selectAllText:getI18nText('全选','全选'),
						 nonSelectedText: getI18nText('请选择','请选择')
					});
				});
			}});
			$("#searchForm").searchData();
		}
		$.views.converters('dateToTime',function(datetime){
			if(!datetime) return "";
			return datetime.toString().substr(11)
		});
		
		$.views.converters('clock',function(time){
			
			return formatClock(time);
			function formatClock(time) {
				if(time == undefined || time == null || time=='') return "0s";
				time = parseInt(time);
				var h = Math.floor(time/3600);
				var m = Math.floor(time%3600/60);
				var s = time%60;
				m = m<10?'0'+m:m;
				s = s<10?'0'+s:s;

				return h+":"+m+":"+s;
			}
		});

		Report.recoredListener = function(serialId) {
			 popup.layerShow({type:2,title:getI18nText('播放录音','播放录音'),offset:'20px',area:['750px','450px']},"/yc-base/pages/record/record-play.jsp",{serialId:serialId,source:'${param.source}'});
		}
		
		Report.videoListener = function(serialId,billTime) {
			 popup.layerShow({type:2,title:'播放视频',offset:'20px',area:['800px','480px']},"/yc-base/pages/record/video-play.html",{serialId:serialId,billTime:billTime});
		}
		
		Report.ivrListener = function(serialId,called,_called){
			called = getPhone(called,_called);
			if(parent.CallControl&&parent.CallControl.getState() == 'TALK'){
				var callType = 2;
				var source = 'play';
				var userData ={
						serialId:serialId
				}
				parent.CallControl.consultIVR(called, source, userData, function(data){console.info(data)})
			}else{
				layer.alert(getI18nText('不可IVR播放。','非【通话中】状态，不可IVR播放。'));
			}
		}
		
		Report.changeTask = function(){
			Report.loadData();
		}

		Report.exportEntRepotList = function(){
			var data = form.getJSONObject("#searchForm");
			if(!checkDate(data,"startTime","endTime",31)){
				return;
			}
			var agentId = "";
			if(data.agentId){
				//var agentIds = JSON.stringify(data.agentId);
				for(var i = 0;i < data.agentId.length;i++){
					agentId = agentId+data.agentId[i]+",";
				}
				agentId = agentId.substring(0, agentId.length-1);
			}
			var msg = getI18nText('是否导出通话记录','是否导出通话记录？');
			layer.confirm(msg,{icon: 3, title:getI18nText('导出提示'),offset:'20px'}, function(index){
				layer.close(index);
				<%--location.href = "${ctxPath}/servlet/export?action=exportEntRepotList&"+$("#searchForm").serialize()+"&agentIds="+agentId;--%>
				windowOpen('${ctxPath}/servlet/report/export?action=exportEntRepotList&'+$("#searchForm").serialize()+'&agentIds='+agentId,getI18nText('通话记录查询结果')+'.xlsx');
			});
			// var totalRow = $(".totalRow").text();
			// if(limitExport(totalRow)){
			// }
		}

		/**
		 * 导出进度查询
		 */
		var openPerIndex;//进度条标识

		Report.exportRepotFiles = function(){
			var msg = getI18nText('是否下载当前页面的通话录音','是否下载当前页面的通话录音？');
			layer.confirm(msg,{icon: 3, title:getI18nText('下载提示'),offset:'20px'}, function(index){
				layer.close(index);
				<%--location.href = "${ctxPath}/servlet/export?action=exportRepotFiles&files="+files;			--%>
				windowOpen('${ctxPath}/servlet/export?action=exportRepotFiles&'+$("#searchForm").serialize(),getI18nText('录音文件')+'.zip',function () {
					let percent = 0;
					ajax.remoteCall("${ctxPath}/servlet/export?query=exportProgress&exportType=actionForExportRepotFiles",{},function(result) {
						if(result.state == 1){
							var expData = result.data;
							percent = expData.percent;
						}
					},{async:false});
					return percent;
				}, {timeout:2000});
			});
		}

		Report.exportProgress = function(exportType,fn1){
			ajax.remoteCall("${ctxPath}/servlet/export?query=exportProgress&exportType="+exportType,{},function(result) {
				if(result.state == 1){
					var expData = result.data;
					$(".layer_notice").text("正在导出，请勿关闭页面或刷新，当前进度："+expData.percentage);
					if(!openPerIndex){
						//打开进度条（捕获页）
						openPerIndex = layer.open({
							type: 1,
							shade: false,
							title: "导出进度", //不显示标题
							offset: '300px',//只定义top坐标，水平保持居中
							content: $('.layer_notice') //捕获的元素，注意：最好该指定的元素要存放在body最外层，否则可能被其它的相对元素所影响
							,cancel:function(){// 右上角关闭事件的逻辑
								//清除计时器
								clearInterval(checkSync);
								checkSync=null;
								openPerIndex=null;
							}
						})
					}

					if(checkSync==null){
						checkSync = setInterval(function(){
							Report.exportProgress(exportType);
						},3000);
					}
				}else{
					//关闭进度条
					layer.close(openPerIndex);
					openPerIndex = null;
					//清除计时器
					clearInterval(checkSync);
					checkSync=null;
					fn1&&fn1();
				}
			});
		}

		Report.exportRepotAllFiles = function(){
			var msg = getI18nText('是否批量下载当前范围内通话录音','是否批量下载当前范围内通话录音？');
			layer.confirm(msg,{icon: 3, title:getI18nText('下载提示'),offset:'20px'}, function(index){
				var data = form.getJSONObject("#searchForm");
				ajax.remoteCall("${ctxPath}/servlet/export?action=zipFiles",data,function(result) {
					if(result.state == 1){
						var msg = getI18nText('正在处理数据','正在处理数据，请不要关闭页面！');
						layer.msg(msg,{icon: 1},function(){
							Report.exportRepotFilesTimer(result.data);
						});
					}else{
						layer.alert(result.msg);
					}
				});
			});
		}
		Report.exportRepotFilesTimer = function(reqId){
			layer.load(1, {shade: [0.1,'#000'],time:0});
			 var time = setInterval(function() {
				 var dataStr=JSON.stringify({reqId:reqId});
				 if(typeof(filterXSS)!="undefined"){
					dataStr=filterXSS(dataStr);
				 }
				 $.ajax( {
						url : '${ctxPath}/servlet/export?action=zipFilesPro',type : 'post',cache:false,dataType : 'json',contentType : "application/x-www-form-urlencoded; charset=UTF-8",
						data : {data : dataStr},
						success : function(result) {
							if(result.state == 1){
								var data = result.data;
								if(data){
									if(data['succ'] == 0){
										console.info(data)
									}else if(data['succ'] == 1){
										layer.closeAll('loading');
										clearInterval(time);
										<%--location.href = "${ctxPath}/servlet/export?action=downRecordZip&reqId="+reqId;	--%>
										windowOpen('${ctxPath}/servlet/export?action=downRecordZip&reqId='+reqId,files,getI18nText('录音文件')+'.zip');
									}else{
										layer.closeAll('loading');
										clearInterval(time);
									}
								}else{
									layer.closeAll('loading');
									clearInterval(time);
								}
							}else{
								layer.closeAll('loading');
								clearInterval(time);
								layer.alert(result.msg);
							}
						},error : function(jqXHR, textStatus, errorThrown) {
							console.error("HTTP Status "  + jqXHR.status + " - " +textStatus+ " - "+url+"");
							var msg = getI18nText('网络故障',"出现网络故障,请稍后再试!");
							layer.alert("出现网络故障,请稍后再试!",{icon:7,time:15000});
						},complete :function(){

						},beforeSend :function(xhr){
						}
						,headers:{'token':'bWFyc0AyMDE5'}}).then(function(result){

					});
		     }, 3000);
		}

		Report.showOrderDetail = function(orderId,custObjId,tmpId) {
			popup.layerShow({type:2,title:getI18nText('工单详情'),offset:'rb',area:['100%','100%'],maxmin:true},"${ctxPath}/pages/order2/order/my/order-info.jsp",{orderId:orderId});
		}

		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$("#more").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up");
		}
		function fnAgentInfo(row){
			var info = '';
			var name = row.AGENT_NAME;
			var phone = row.AGENT_PHONE;

			if(name && name != '' && name != undefined){
				info = name;
			}
			if(phone && phone != '' && phone != undefined){
				if(info != ''){
					info += '-' + phone;
				}else{
					info = phone;
				}
			}
			return info;
		}
		function createCause(createCause){
			//1 呼出(6,8)  2 呼入(1,2,4) 3 席间呼入(14)  4 席间呼出(29)  5 咨询呼入(5)  6 转移呼入(3,9,10) 7 转移呼出(99) 8 其他外呼（19）
			if(createCause == 6 || createCause == 8){//呼出
				return getI18nText('呼出');
			}else if(createCause == 2||createCause == 1||createCause == 4){//呼入
				return getI18nText('呼入');
			}else if(createCause == 5){//咨询呼入
				return getI18nText('咨询');
			}else if(createCause == 14){//席间呼入
				return getI18nText('席间呼入');
			}else if(createCause == 29){//席间呼出
				return getI18nText('席间呼出');
			}else if(createCause == 3||createCause == 9||createCause == 10){//转移呼入
				return getI18nText('呼转外线');
			}else if(createCause == 99){//呼转外线
				return getI18nText('转移呼出');
			}else{
				return getI18nText('其他外呼');
			}
		}

		//播放ivr录音
		function fnIvrPaly(row){
			if(ivrPaly&&ivrPaly=='1'){
				return '<a class="IVR_PALY" href="javascript:void(0)" onclick="Report.ivrListener(\''+row.SERIAL_ID+'\',\''+row.CUST_PHONE+'\',\''+row._CUST_PHONE+'\')" data-i18n="IVR播放">IVR播放</a>';
			}else{
				return '';
			}
		}
		function showOrderInfo(row){
			var html = '';
			var orderVersion = localStorage.getItem("orderVersion");
			if('v2' == orderVersion){
				html = '<a href="javascript:void(0)" onclick="Report.showOrderDetail(\''+row.ORDER_ID+'\',\''+row.CUST_OBJ_ID+'\',\''+row.TMP_ID+'\',\''+row.CUST_PHONE+'\')"><span data-i18n="工单详情">工单</span></a>';
			}
			return html;
		}

</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_layui.jsp"%>