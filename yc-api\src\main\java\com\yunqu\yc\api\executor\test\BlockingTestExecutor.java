package com.yunqu.yc.api.executor.test;

import com.yunqu.yc.api.executor.AbstractExecutor;
import com.yunqu.yc.api.log.TaskLogger;

/**
 * 用于测试线程池阻塞的测试执行器
 * 长时间阻塞线程，用于测试线程池耗尽的情况
 */
public class BlockingTestExecutor extends AbstractExecutor {
    
    private String sessionId;
    private long blockTime; // 阻塞时间（毫秒）
    private int executorId;
    private static volatile boolean stopFlag = false; // 停止标志
    
    public BlockingTestExecutor(String sessionId, long blockTime, int executorId) {
        this.sessionId = sessionId;
        this.blockTime = blockTime;
        this.executorId = executorId;
    }
    
    /**
     * 设置停止标志，用于停止所有阻塞执行器
     */
    public static void setStopFlag(boolean stop) {
        stopFlag = stop;
        TaskLogger.getLogger().info("[TEST] BlockingTestExecutor 停止标志设置为: " + stop);
    }
    
    @Override
    public void execute() throws Exception {
//        TaskLogger.getLogger().info("[TEST] BlockingTestExecutor[" + executorId + "] 开始阻塞，预计阻塞时间: " + blockTime + "ms");

        long startTime = System.currentTimeMillis();
        long endTime = startTime + blockTime;
        
        try {
            // 循环检查停止标志，每秒检查一次
            while (System.currentTimeMillis() < endTime && !stopFlag) {
                Thread.sleep(1000);
                
                // 每10秒输出一次状态
//                if ((System.currentTimeMillis() - startTime) % 10000 < 1000) {
//                    long elapsed = System.currentTimeMillis() - startTime;
//                    TaskLogger.getLogger().info("[TEST] BlockingTestExecutor[" + executorId + "] 已阻塞: " + elapsed + "ms");
//                }
            }
            
            long actualTime = System.currentTimeMillis() - startTime;
            
            if (stopFlag) {
                TaskLogger.getLogger().info("[TEST] BlockingTestExecutor[" + executorId + "] 收到停止信号，提前结束，实际阻塞时间: " + actualTime + "ms");
            } else {
                TaskLogger.getLogger().info("[TEST] BlockingTestExecutor[" + executorId + "] 阻塞完成，实际阻塞时间: " + actualTime + "ms");
            }
            
        } catch (InterruptedException e) {
            TaskLogger.getLogger().warn("[TEST] BlockingTestExecutor[" + executorId + "] 被中断");
            Thread.currentThread().interrupt();
        }
    }
    
    @Override
    public String getSessionId() {
        return sessionId;
    }
    
    @Override
    public String toString() {
        return "BlockingTestExecutor[id=" + executorId + ", sessionId=" + sessionId + 
               ", blockTime=" + blockTime + "ms]";
    }
}
