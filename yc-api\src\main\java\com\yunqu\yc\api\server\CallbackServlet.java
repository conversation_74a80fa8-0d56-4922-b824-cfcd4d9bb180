package com.yunqu.yc.api.server;



import java.io.DataInputStream;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.alarm.MarsAlarm;
import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.base.QueryFactory;
import com.yunqu.yc.api.context.EntContext;
import com.yunqu.yc.api.executor.EventDispatcher;
import com.yunqu.yc.api.executor.impl.NotifyExecutor;
import com.yunqu.yc.api.log.ApiLogger;
import com.yunqu.yc.api.log.CallbackLogger;
import com.yunqu.yc.api.util.HCodeUtil;
import com.yunqu.yc.api.util.TaskObjTableUtils;

/**
 * 处理业务回调的结果,格式： url?data={result:result,userData:userData} 满意度：1 非常满意 2 满意 3 一般
 * 4 不满意
 * 
 * <AUTHOR>
 *
 */
@WebServlet("/callback")
public class CallbackServlet extends HttpServlet {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response) {
		doPost(request, response);
	}
	
	private EasyQuery getQuery(String entId){
		EasyQuery easyQuery = QueryFactory.getWriteQuery(entId);
		easyQuery.setLogger(CallbackLogger.getLogger());
		return easyQuery;
	}
	
	private String getBody(HttpServletRequest request){
		int contLength = request.getContentLength();
		if(contLength<=0){
			return "";
		}
		byte[] content  = new byte[request.getContentLength()];
		try {
			DataInputStream dis = new DataInputStream(request.getInputStream());
			dis.readFully(content);
			String  data = new String(content,"GBK");
			return data;
		} catch (Exception ex) {
			return "";
		}
	}
	
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response) {
		JSONObject jsonObject =  null;
		String sipCallId = null;
		try {
			String data = request.getParameter("data");
			if(StringUtils.isBlank(data)){
				String body = this.getBody(request);
				if(body.startsWith("data=")){
					data = body.substring("data=".length());
				}
			}
			
			ApiLogger.getLogger().info("[CallbackServlet]["+request.getRemoteAddr()+"] << " + data);
			CallbackLogger.getLogger().info("[CallbackServlet]["+request.getRemoteAddr()+"] << " + data);

			if (StringUtils.isBlank(data)) {
				this.write(response,request, "Data is null!");
				return;
			}
			
			try {
				jsonObject =  JSONObject.parseObject(data);
				if (jsonObject == null) {
					MarsAlarm.ivrAlarm(request.getRemoteAddr(), data);
					this.write(response,request, "Data is not valid json format!");
					return;
				}
			} catch (Exception ex) {
				MarsAlarm.ivrAlarm(request.getRemoteAddr(), data);
			}
			
			
			JSONObject userData = jsonObject.getJSONObject("userData");
			String  command = request.getParameter("command");
			
			//如果命名参数为空，则从userData中获取。
			if (StringUtils.isBlank(command)) {
				if (userData == null) {
					this.write(response,request, "param[userData] can not null!");
					return;
				}
				command = userData.getString("command");
			}
			if (StringUtils.isBlank(command)) {
				this.write(response,request, "param[command] can not null!");
				return;
			}
			
			jsonObject.put("command", command);
			
			String result = jsonObject.getString("result");
			
			//满意度调查结果
			if ("respSatisf".equalsIgnoreCase(command)) {
				
				if (StringUtils.isBlank(result)) {
					this.write(response,request, "param[result] can not null!");
					return;
				}
				
				String entId = userData.getString("entId");
				String sql = "";
				
				// 默认使用callSerialId，如果ivr传入sipCallId则根据sipCallId修改满意度
				String callSerialId = userData.getString("callSerialId");
				String busiId = callSerialId;
				sipCallId = jsonObject.getString("sipCallId");
				if(StringUtils.isNotBlank(sipCallId)) {
					busiId = sipCallId;
					sql = "update " + EntContext.getContext(entId).getTableName("CC_CALL_RECORD")
							+ " set SATISF_ID = ? where SIP_CALL_ID = ?";
				} else {
					sql = "update " + EntContext.getContext(entId).getTableName("CC_CALL_RECORD")
							+ " set SATISF_ID = ? where SERIAL_ID = ?";
				}
				
				try {
					this.getQuery(entId).executeUpdate(sql, new Object[] { Integer.parseInt(result), busiId });
				} catch (Exception ex) {
					ApiLogger.getLogger().error(ex, ex);
					CallbackLogger.getLogger().error(ex, ex);
					this.write(response,request, "update error,cause:" + ex.getMessage());
					return;
				}
				this.write(response,request, "Succ");
				return;
			}
			
			//智能IVR任务执行结果
			if("respTaskResult".equalsIgnoreCase(command)){
				String entId  =  userData.getString("entId");
				String taskId =  userData.getString("taskId");
				String objId  =  userData.getString("objId");
				String sql = "select RESULT_ID from "+EntContext.getContext(entId).getTableName("CC_TASK_SALE_RESULT")+" where ENT_ID = ? and TASK_ID = ?  and  IDX_ORDER = ? ";
				JSONObject resultObj = this.getQuery(entId).queryForRow(sql, new Object[]{entId,taskId,result},new JSONMapperImpl());
				if(resultObj == null){
					this.write(response,request, "Not found task["+taskId+"] resultId :" +result);
					return ;
				}
				String saleResultId=resultObj.getString("RESULT_ID");
				EasyRecord obj = new EasyRecord(TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ"), "OBJ_ID");
				obj.setPrimaryValues(objId);
				obj.set("SALE_FINISH_TIME", EasyDate.getCurrentDateString());
				obj.set("RUN_DATE", EasyCalendar.newInstance().getDateInt());
				obj.set("TASK_STATE","9");
				obj.set("AGENT_FLAG","1");
				obj.set("RESULT_ID", saleResultId);
				obj.set("OBJ_MEMO", "");
				obj.set("CUST_SATISFY", "2");
				obj.set("QC_STATE", "1");
				obj.set("AGENT_ID", "0");
				obj.set("CALL_RESULT", "0");
				obj.set("IVR_SALE_RESULT", 0);
				try {
					this.getQuery(entId).update(obj);
				} catch (Exception e) {
					ApiLogger.getLogger().error("respTaskResult callback(" + request.getRemoteAddr() + ") cause:" + e.getMessage(), e);
					CallbackLogger.getLogger().error("respTaskResult callback(" + request.getRemoteAddr() + ") cause:" + e.getMessage(), e);
					this.write(response,request, "save error,cause:" + e.getMessage());
				}
				return;
				
			}
			
			
			//ivr订购结果
			if ("respIVROrder".equalsIgnoreCase(command)) {
				String objId = userData.getString("objId");
				String entId = userData.getString("entId");
				String taskId =  userData.getString("taskId");
				
				if (StringUtils.isBlank(result)) {
					this.write(response,request, "param[result] can not null!");
					return;
				}
				
				if (StringUtils.isBlank(objId)) {
					this.write(response,request, "param[objId] can not null!");
					return;
				}
				String sql = "update " + TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")
						+ " set IVR_SALE_RESULT = ? where OBJ_ID = ?";
				try {
					this.getQuery(entId).executeUpdate(sql, new Object[] { result, objId });
				} catch (Exception ex) {
					this.write(response,request, "update error,cause:" + ex.getMessage());
					ApiLogger.getLogger().error("respIVROrder callback(" + request.getRemoteAddr() + ") cause:" + ex.getMessage(), ex);
					CallbackLogger.getLogger().error("respIVROrder callback(" + request.getRemoteAddr() + ") cause:" + ex.getMessage(), ex);
				}
				return ;
			}
			
			
			/**
			 * {"recordId":"20180628163230","caller":"15250775131","called":"05923228863","entId":"1001","stepId":"3"
			 * ,"recordFile":"rec1001/20180628/15250775131_05923228863_163230.wav","beginTime":"2018-06-28 16:32:12",
			 * "ivrEndTime":"2018-06-28 16:32:35"}) 

			 */
			
			
			/**
create table CC_CALL_MISCALL
(
   SERIAL_ID            varchar(32) not null comment '呼叫流水',
   ENT_ID               varchar(32) comment '企业ID',
   DATE_ID              int comment '日期ID，格式：yyyymmdd',
   CALLER               varchar(30) comment '主叫',
   CALLED               varchar(30) comment '被叫',
   BEGIN_TIME           varchar(19) comment '开始时间',
   QUEUE_TIME           varchar(19) comment '进入排队时间',
   END_TIME             varchar(19) comment '结束时间',
   QUEUE_STAY_TIME      int default 0 comment '排队停留时间，单位：秒',
   TOTAL_TIME           int comment '总时长，结束-开始时间',
   CLEAR_CAUSE          int comment '挂机原因， 0 未按键挂机 1 IVR自助服务挂机  2 用户排队挂机  3  坐席振铃挂机',
   CALL_STEP_ID         int comment '挂机环节（废弃），0 未按键挂机  1 IVR自助服务挂机 2 排队挂机 ',
   GROUP_ID             varchar(32) comment '技能组ID',
   GROUP_NAME           varchar(50) comment '技能组名称',
   STATE                int default 0 comment '处理状态，0 未处理  1 已经处理  ',
   USER_ID              varchar(32) comment '处理人',
   USERNAME             varchar(50) comment '处理人姓名',
   HANDLE_TIME          varchar(19) comment '处理时间',
   RECALL_TIME          varchar(19) comment '回拨时间',
   CALL_SERIAL_ID       varchar(32) comment '通话记录',
   AREA_CODE            varchar(30) comment '客户来电地区，保持客户来电的区号信息。',
   HANDLE_DEPT_CODE     varchar(30) comment '处理部门',
   QUEUE_ID             int comment '队列ID，V3版本提供。',
   QUEUE_NAME           varchar(50) comment '队列名称，V3版本提供。',
   KEY_CODE             varchar(10) comment 'IVR按键，保存IVR转人工的入口按键，userData.ivrKeyCode',
   AGENT_RELEASE        int default 1 comment '结束通话方式，1  用户挂断  2 坐席挂断 3 系统挂断',
   SIP_CALL_ID          varchar(100) comment 'SIP_CALL_ID',
   primary key (SERIAL_ID)
);

			 alter table CC_CALL_MISCALL add GROUP_BUSY_COUNT int default 0 comment '置忙数';
			 alter table CC_CALL_MISCALL add GROUP_IDEL_COUNT int default 0 comment '置闲数';
			 * 
			 */
			//漏话 和 留言
			if ("respNoanswer".equalsIgnoreCase(command)) {
				String caller = HCodeUtil.formatCaller(jsonObject.getString("caller")); //主叫
				String called = jsonObject.getString("called");  //被叫
				String entId = jsonObject.getString("entId");  //企业Id
				String beginTime = jsonObject.getString("beginTime"); //开始时间
				String endTime = jsonObject.getString("ivrEndTime");  //结束时间
				// '挂机环节，1 IVR挂机  2 排队挂机  3 录音（留言）挂机  4 自助服务挂机  5 转外线挂机',
				int stepId = jsonObject.getIntValue("stepId");   
				String groupId = jsonObject.getString("groupId");  //所属技能组
				String recordId = jsonObject.getString("recordId");  //录音ID
				String recordFile = jsonObject.getString("recordFile"); /// 录音文件
				String videoFile = StringUtils.trimToEmpty(jsonObject.getString("videoFile"));
				String ivrQueueTime = jsonObject.getString("ivrQueueTime"); /// IVR排队时间
				String keyCode = jsonObject.getString("keyCode");   //最后按键，如：8号键转A机器人  9号键转B机器人 
				//结束通话方式，1  用户挂断  2 坐席挂断 3 系统挂断
				sipCallId = jsonObject.getString("sipCallId");
				int agentRelease  = jsonObject.getIntValue("agentRelease"); // 1  用户挂断  2 坐席挂断 3 系统挂断',
				
				String origCaller = StringUtils.trimToEmpty(jsonObject.getString("origCaller"));//原主叫
				String origCalled = StringUtils.trimToEmpty(jsonObject.getString("origCalled"));//原被叫
				
				int queueId = jsonObject.getIntValue("queueId");
				
				String agentId = jsonObject.getString("agentId");
				
				String busiId = jsonObject.getString("busiId");

				int queueStayTime = 0;

				if (StringUtils.isBlank(jsonObject.getString("stepId"))) {
					this.write(response,request, "Param[stepId] is null!");
					return;
				}
				
//				// 优先接口传参
//				if(StringUtils.isBlank(clearCause)) {
//					clearCause = "1";
//					if("3".equals(agentRelease)){
//						clearCause = "2";
//					}
//				}
				// 挂机原因， 0 未按键挂机 1 IVR自助服务挂机  2 用户排队挂机  3  坐席振铃挂机 4 转接外线挂机',
				int _clearCause = 0;
				//1  IVR未按键挂机  
				if(1 == stepId) _clearCause = 0;  // 0  未按键挂机
				//2 排队挂机 
				if(2 == stepId) _clearCause = 2;  // 2  用户排队挂机
//				//3 录音（留言）挂机  
//				if("3".equals(stepId)) _clearCause = "3";   // 3  坐席振铃挂机 
				// 4 自助服务挂机
				if(4 == stepId) _clearCause = 1;  // 1 IVR自助服务挂机
				//5 转外线挂机
				if(5 == stepId) _clearCause = 4;  // 4  转接外线挂机
				//3 坐席话机振铃挂机
				if(6 == stepId) _clearCause = 3;  // 3  坐席振铃挂机 
				
				
				//计算用户排队等候时间
				EasyCalendar cal = EasyCalendar.newInstance(beginTime,"yyyy-MM-dd HH:mm:ss");
				
				if(isBlankDateTime(ivrQueueTime)||isBlankDateTime(endTime)){
				}else{
					queueStayTime = cal.diff(ivrQueueTime,endTime, "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
				}
				
				int totalTime = 0 ;
				if(isBlankDateTime(beginTime)||isBlankDateTime(endTime)){
				}else{
					totalTime = cal.diff(beginTime,endTime, "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
				}
				
				
				if (StringUtils.isBlank(called)) {
					this.write(response,request, "Param[called] is null!");
					return;
				}
				if (StringUtils.isBlank(entId)) {
					this.write(response,request, "Param[entId] is null!");
					return;
				}
				if (StringUtils.isBlank(beginTime)) {
					this.write(response,request, "Param[beginTime] is null!");
					return ;
				}
				
				try {
					EntContext entContext = EntContext.getContext(entId); 

					//添加振铃时间和振铃时长；
					String alertingTime = jsonObject.getString("agentAleringTime");
					String ivrQueueEndTime = endTime;
					int agentStayTime = 0;
					if(StringUtils.isNotBlank(alertingTime)){
						agentStayTime = cal.diff(alertingTime,endTime, "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
						ivrQueueEndTime = alertingTime;
					}
					if(StringUtils.isNotBlank(ivrQueueTime)){
						queueStayTime = cal.diff(ivrQueueTime,ivrQueueEndTime, "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );	
					}
					
					EasyRecord record = null;
					if(3 == stepId){ //留言挂机
						record = new EasyRecord(entContext.getTableName("CC_CALL_WORD"));
						
						record.set("WORD_TIME", jsonObject.getString("wordTime"));
						String replyPhone = jsonObject.getString("replyPhone");
						if(StringUtils.isBlank(replyPhone)){
							replyPhone = caller;
						}
						record.set( "REPLY_PHONE", replyPhone);
					}else if(StringUtils.isNotBlank(agentId) && StringUtils.isNotBlank(alertingTime) && agentStayTime >= Constants.getNoanswerAlteringTime()){
						record = new EasyRecord(entContext.getTableName("CC_CALL_NOANSWER"));
						record.set( "ALERING_TIME", alertingTime);
						record.set("AGENT_STAY_TIME", agentStayTime);
					}else{
						record = new EasyRecord(entContext.getTableName("CC_CALL_MISCALL"));
						record.set("ALERTING_TIME", alertingTime);
						record.set("AGENT_STAY_TIME", agentStayTime);

						record.set("GROUP_BUSY_COUNT", StringUtils.trimToEmpty(jsonObject.getString("groupBusyCount")));
						record.set("GROUP_IDEL_COUNT", StringUtils.trimToEmpty(jsonObject.getString("groupIdelCount")));
					}
					
					record.set("SERIAL_ID", RandomKit.uniqueStr());
					record.set("TOTAL_TIME", totalTime);
					record.set("DATE_ID", cal.getDateInt());
					record.set("CALLER", caller);
					record.set("CALLED", called);
					record.set("ENT_ID", entId);
					record.set("BEGIN_TIME", beginTime);
					record.set("END_TIME", endTime);

					record.set("CLEAR_CAUSE", _clearCause);//'挂机原因， 0 未按键挂机 1 IVR自助服务挂机  2 用户排队挂机  3  坐席振铃挂机 4 转接外线挂机',
					record.set("CALL_STEP_ID", stepId);
					record.set("SIP_CALL_ID", sipCallId);
					record.set("ORIG_CALLER", origCaller);
					record.set("ORIG_CALLED", origCalled);
					record.set("RECORD_FILE", recordFile);
					record.set("RECORD_ID", recordId);
					record.set("VIDEO_FILE", videoFile);
					record.set("AREA_CODE", HCodeUtil.getAracodeByCalled(caller));
					record.set("AGENT_RELEASE", agentRelease);
					record.set("GROUP_ID", groupId);
					record.set("GROUP_NAME", entContext.getGroupName(groupId));
					record.set( "DEPT_CODE", entContext.getGroupCode(groupId));
					record.set( "QUEUE_ID", queueId);
					record.set( "QUEUE_NAME", entContext.getQueueName(String.valueOf(queueId)));
					record.set( "KEY_CODE", keyCode);//保持最后的按键
					record.set( "AGENT_ID", agentId);
					record.set( "AGENT_NAME", entContext.getAgentName(agentId));
					record.set( "BUSI_ORDER_ID", entContext.getBusiOrder(busiId));
					record.set( "QUEUE_STAY_TIME", queueStayTime);
					record.set( "QUEUE_TIME", ivrQueueTime);

					EntContext.getContext(entId).checkRecord(record);
					
					this.getQuery(entId).save(record);
					
					jsonObject.put("callSerialId", record.getString("SERIAL_ID"));
				} catch (Exception ex) {
					this.write(response,request, "run error,cause:" + ex.getMessage());
					CallbackLogger.getLogger().error("CC_CALL_MISCALL callback(" + request.getRemoteAddr() + ") cause:" + ex.getMessage(), ex);
				}
				return ;
			}
			

		} catch (Exception ex) {
			this.write(response,request, "update error,cause:" + ex.getMessage());
			CallbackLogger.getLogger().error(ex, ex);
		}
		if(jsonObject!=null){
			EventDispatcher.addEvent(new NotifyExecutor("IVR_CALLBACK_NOTIFY", sipCallId, jsonObject));
		}
	}
	
	private boolean isBlankDateTime(String dateTime){
		if(StringUtils.isBlank(dateTime)) return true;
		if(dateTime.startsWith("1970")) return true;
		if(dateTime.length() <19) return false;
		return false;
	}

	private void write(HttpServletResponse response, HttpServletRequest request,String content) {
		try {
			ApiLogger.getLogger().info("[CallbackServlet]["+request.getRemoteAddr()+"] >> " + content);
			CallbackLogger.getLogger().info("[CallbackServlet]["+request.getRemoteAddr()+"] >> " + content);
			response.getWriter().write(content);
		} catch (Exception ex) {
			CallbackLogger.getLogger().error(ex, ex);
		}
	}
	

}
