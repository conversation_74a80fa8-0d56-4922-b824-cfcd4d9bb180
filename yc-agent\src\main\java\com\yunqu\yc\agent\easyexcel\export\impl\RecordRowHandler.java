package com.yunqu.yc.agent.easyexcel.export.impl;

import com.yunqu.yc.agent.easyexcel.export.BaseRowHandler;
import com.yunqu.yc.agent.utils.DictUtil;
import com.yunqu.yc.agent.utils.I18nUtil;
import com.yunqu.yc.agent.utils.ParamUtil;
import com.yunqu.yc.agent.utils.PhoneCryptor;

import org.easitline.common.utils.string.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 通话记录
 * <AUTHOR>
 */
public class RecordRowHandler extends BaseRowHandler {
	private String resEntId = null;

	private Map<String, String> skillDict = null;
	private Map<String, String> clearCauseDict = null;

	public RecordRowHandler(String resEntId,Map<String, String> skillDict,Map<String, String> clearCauseDict){
		this.resEntId = resEntId;
		this.skillDict = skillDict;
		this.clearCauseDict = clearCauseDict;
	}

	/**
	 * 获取表名
	 */
	@Override
	public String getName() {
		return "通话记录列表";
	}

	/**
	 * 获取表头
	 */
	@Override
	public List<String> getHead() {
		List<String> headers = new ArrayList<String>();
        headers.add(I18nUtil.getMessage(getLang(), "日期"));
        headers.add(I18nUtil.getMessage(getLang(), "开始时间"));
        headers.add(I18nUtil.getMessage(getLang(), "结束时间"));
        headers.add(I18nUtil.getMessage(getLang(), "通话时长"));
        headers.add(I18nUtil.getMessage(getLang(), "计费时长（分钟）"));
        headers.add(I18nUtil.getMessage(getLang(), "坐席"));
        headers.add(I18nUtil.getMessage(getLang(), "话机号码"));
        headers.add(I18nUtil.getMessage(getLang(), "主叫号码"));
        headers.add(I18nUtil.getMessage(getLang(), "被叫号码"));
        headers.add(I18nUtil.getMessage(getLang(), "号码归属地"));
        headers.add(I18nUtil.getMessage(getLang(), "技能组"));
        headers.add(I18nUtil.getMessage(getLang(), "呼叫方向"));
        headers.add(I18nUtil.getMessage(getLang(), "呼叫结果"));
        headers.add(I18nUtil.getMessage(getLang(), "挂机类型"));
        headers.add(I18nUtil.getMessage(getLang(), "满意度"));
        headers.add(I18nUtil.getMessage(getLang(), "质检"));
		return headers;
	}
	
	/**
	 * 解析每行数据
	 * @param map
	 * @return
	 */
	@Override
	public List<String> parseRow(Map<String, String> map){
        List<String> list = new ArrayList<String>();
        list.add(map.get("DATE_ID"));
        list.add(StringUtils.isNotBlank(map.get("BILL_BEGIN_TIME")) ? map.get("BILL_BEGIN_TIME").substring(11) : "-");
        list.add(StringUtils.isNotBlank(map.get("BILL_END_TIME")) ? map.get("BILL_END_TIME").substring(11) : "-");
        list.add(ParamUtil.formatTimeDesc(map.get("BILL_TIME")));
        list.add(map.get("FEE_TIME_60"));
        list.add(map.get("AGENT_NAME") + "-" + map.get("AGENT_PHONE"));
        list.add(map.get("PHONE_NUM"));
        list.add(PhoneCryptor.getInstance().decryptExpor(resEntId, map.get("CALLER")));
        list.add(PhoneCryptor.getInstance().decryptExpor(resEntId, map.get("CALLED")));
        list.add(DictUtil.getAreaName(map.get("AREA_CODE")));
        list.add(skillDict.get(map.get("GROUP_ID")));
        list.add(I18nUtil.getMessage(getLang(), DictUtil.parseCreateCause(map.get("CREATE_CAUSE"))));
        list.add(I18nUtil.getMessage(getLang(), DictUtil.parseClearCause(clearCauseDict,map.get("CLEAR_CAUSE"))));
        list.add(I18nUtil.getMessage(getLang(), DictUtil.parseAgentRelease(map.get("AGENT_RELEASE"))));
        list.add(I18nUtil.getMessage(getLang(), DictUtil.parseSatisfId(map.get("SATISF_ID"))));
        list.add(I18nUtil.getMessage(getLang(), DictUtil.parseQcState(map.get("QC_STATE"))));
        return list;
	}

	@Override
	public void start() {
	}

	@Override
	public void done() {
		
	}

}
