package com.yunqu.yc.api.executor.test;

import com.yunqu.yc.api.executor.AbstractExecutor;
import com.yunqu.yc.api.log.TaskLogger;

/**
 * 用于测试执行超时的测试执行器
 * 可以模拟不同的执行时间来测试超时告警机制
 */
public class TimeoutTestExecutor extends AbstractExecutor {
    
    private String sessionId;
    private long sleepTime; // 执行时间（毫秒）
    private String testType;
    private int executorId;
    
    public TimeoutTestExecutor(String sessionId, long sleepTime, String testType, int executorId) {
        this.sessionId = sessionId;
        this.sleepTime = sleepTime;
        this.testType = testType;
        this.executorId = executorId;
    }
    
    @Override
    public void execute() throws Exception {

        long startTime = System.currentTimeMillis();
        
        try {
            // 模拟执行时间
            Thread.sleep(sleepTime);

        } catch (InterruptedException e) {
            TaskLogger.getLogger().warn("[TEST] TimeoutTestExecutor[" + executorId + "] 被中断");
            Thread.currentThread().interrupt();
        }
    }
    
    @Override
    public String getSessionId() {
        return sessionId;
    }
    
    @Override
    public String toString() {
        return "TimeoutTestExecutor[id=" + executorId + ", sessionId=" + sessionId + 
               ", sleepTime=" + sleepTime + "ms, testType=" + testType + "]";
    }
}
