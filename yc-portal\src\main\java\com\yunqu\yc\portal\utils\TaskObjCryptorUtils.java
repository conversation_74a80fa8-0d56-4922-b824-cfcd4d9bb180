package com.yunqu.yc.portal.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.log.PortalLogger;
import com.yunqu.yc.portal.utils.crypt.CryptService;
import com.yunqu.yc.portal.utils.crypt.impl.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 外呼名单加密工具类
 */
public class TaskObjCryptorUtils {

    Map<String, Class<? extends CryptService>> cryptMap;

    private static class Holder {
        private static TaskObjCryptorUtils cryptor = new TaskObjCryptorUtils();
    }

    private TaskObjCryptorUtils(){
        cryptMap = new HashMap<>();
        cryptMap.put("phone", PhoneCryptServiceImpl.class);
        cryptMap.put("userName", NameCryptServiceImpl.class);
        cryptMap.put("idcard", IdCardCryptServiceImpl.class);
        cryptMap.put("bankcard", BankCardCryptServiceImpl.class);
        cryptMap.put("hide", HideCryptServiceImpl.class);
    }

    public static TaskObjCryptorUtils getInstance(){
        return TaskObjCryptorUtils.Holder.cryptor;
    }

    public JSONArray encrypt(JSONArray data, Map<String,JSONObject> encryptMap,boolean showcrypt) {
		String[] fields = new String[]{"TEL_NUM1","TEL_NUM2","TEL_NUM3","TEL_NUM4"};

        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject = data.getJSONObject(i);
            PhoneCryptor.getInstance().decrypt(jsonObject,fields,showcrypt);
            
            if (encryptMap != null && encryptMap.size() > 0){
                //如果开启校验并且校验信息不通过则跳过加密
                if (showcrypt){
                    continue;
                }

                for (String key : encryptMap.keySet()) {
                    jsonObject.put(key,encryptContent(jsonObject.getString(key),encryptMap.get(key).getString("dataType")));
                }
            }
        }

        return data;
    }


    public String encryptContent(String content, String dataType) {
        if (cryptMap.containsKey(dataType)){
            try {
                return cryptMap.get(dataType).getConstructor(new Class[]{}).newInstance().encrypt(content);
            }catch (Exception e){
                PortalLogger.getLogger().error(e.getMessage(),e);
            }
        }
        return content;
    }

}
