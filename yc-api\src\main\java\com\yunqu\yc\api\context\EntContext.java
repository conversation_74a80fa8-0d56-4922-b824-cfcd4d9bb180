package com.yunqu.yc.api.context;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.base.QueryFactory;
import com.yunqu.yc.api.log.ApiLogger;
import com.yunqu.yc.api.util.CacheUtil;
import com.yunqu.yc.api.util.HCodeUtil;

public class EntContext {
	
	private String petraGwUrl;
	private String marsGwUrl;
	private String entId;
	private String schemaId;
	private String marsId;
	private String petraId;
	private String entName;
	private String resEntId;
	private String entType;
	private String entCode;
	private String feeCode;
	private String agentOrderPrefix;
	private String callbackUrl;
	private String agentOrderVox;
	//平台配置的企业指定的OCSName
	private String ocsName;
	//如果平台没有配置指定的ocs，则随机指定一个。
	private String randomOcsName;
	private int    state = 0;   
	private String bpoPrefix;
	
	// '付费方式，1 后付费  2 预付费',
	private   String payType = "1";
	
	private long  timer = System.currentTimeMillis();
	private JSONObject extConf = new JSONObject();
	private Map<String,String> orders = new HashMap<String,String>();
	private Map<String,String> busiOrderIds = new HashMap<String,String>();
	private Map<String,String> queueMap = new HashMap<String,String>();
	private Map<String,JSONObject> groupMap = new HashMap<String,JSONObject>();
	private Map<String,String> agentMap = new HashMap<String,String>();
	
	private Map<String,List<String>> prefixs = new HashMap<String,List<String>>();
	
	private   Map<String,JSONObject> smsTemps = new HashMap<String,JSONObject>();
	
	private   EasyCache cache = CacheManager.getMemcache();
	
	private static Logger logger = ApiLogger.getLogger();
	
	private static final ConcurrentHashMap<String, EntContext> contexts = new ConcurrentHashMap<String, EntContext>();
	
	private ConcurrentHashMap<String, JSONObject> tableField = new ConcurrentHashMap<String, JSONObject>();
	
	/**
	 * 外显号码每小时最大可以外呼次数
	 * @return
	 */
	public int getMaxCallerHourCallCount() {
		return this.extConf.getIntValue("DN_HOUR_MAX_CALL");
	}

	/**
	 * 外显号码每天可以外呼次数
	 * @return
	 */
	public int getMaxCallerDayCallCount() {
		return this.extConf.getIntValue("DN_DAY_MAX_CALL");
	}
	
	public static EntContext getContext(String entId) {
		EntContext context = contexts.get(entId);

		// 检查是否需要刷新（缓存信息每五分钟更新一次）
		if (context != null && System.currentTimeMillis() - context.timer < 600 * 1000) {
			return context;
		}

		// 使用entId作为锁，确保同一企业的初始化串行执行
		synchronized (entId.intern()) {
			context = contexts.get(entId);

			// 双重检查
			if (context != null && System.currentTimeMillis() - context.timer < 600 * 1000) {
				return context;
			}

			if (context == null) {
				// 创建新的EntContext对象，初始化完成后再放入缓存
				EntContext newContext = new EntContext(entId);
				try {
					newContext.init();
					newContext.setRandomOcsName(CacheUtil.get("OCS_" + entId));
					contexts.put(entId, newContext);  // 初始化完成后再放入缓存
					context = newContext;
				} catch (Exception ex) {
					logger.error("EntContext.init() error >>cause:" + ex.getMessage(), ex);
					// 初始化失败，不放入缓存，返回未初始化的对象或null（根据业务需求）
					context = newContext;
				}
			} else {
				// 对于已存在但过期的context，重新初始化
				try {
					context.init();
					context.setRandomOcsName(CacheUtil.get("OCS_" + entId));
				} catch (Exception ex) {
					logger.error("EntContext.init() error >>cause:" + ex.getMessage(), ex);
					// 重新初始化失败，可以考虑移除缓存或保持旧状态
				}
			}
		}

		return context;
	}

	

	/**
	 * 查询表结构
	 * @param tableName
	 * @return
	 */
	private JSONObject getTableInfo(String tableName){
		int indexOf = tableName.indexOf(".");
		if(indexOf>0){
			tableName = tableName.substring(0, indexOf) + tableName.substring(indexOf).toLowerCase();
		}else{
			tableName = tableName.toLowerCase();
		}
		JSONObject table = new JSONObject();
		if(tableField.containsKey(tableName)){
			table = tableField.get(tableName);
		}else{
			table = this.queryTableInfo(tableName);
		}
		return table;
	}
	
	private JSONObject queryTableInfo(String tableName){
		JSONObject table = null;
		if(tableField.containsKey(tableName)){
			table = tableField.get(tableName);
			return table;
		}

		synchronized (tableName.intern()){
			if(tableField.containsKey(tableName)){
				return tableField.get(tableName);
			}
			EasyQuery writeQuery = QueryFactory.getWriteQuery(entId);
			table = new JSONObject();
			String querySql ="SELECT * FROM "+tableName;
			DBTypes dbTypes = writeQuery.getTypes();
			String dbTypeName = dbTypes != null ? dbTypes.toString().toUpperCase() : "";

			if(dbTypes == DBTypes.MYSQL || dbTypeName.contains("MYSQL")){
				querySql += " LIMIT 1";
			}else if(dbTypes == DBTypes.ORACLE || dbTypeName.contains("ORACLE")){
				querySql += " WHERE ROWNUM <= 1";
			}else{
				// 默认使用MySQL语法
				querySql += " LIMIT 1";
			}

			Connection conn =null;
			Statement stmt = null;
			ResultSet rs = null;
			try {
				conn = writeQuery.getConnection();
				stmt = conn.createStatement();
				stmt.setQueryTimeout(15);
				stmt.setMaxRows(1);
				rs = stmt.executeQuery(querySql);
				ResultSetMetaData metaData = rs.getMetaData();
				int columnCount = metaData.getColumnCount();
				for (int i = 1; i <= columnCount; i++) {
					String columnName = metaData.getColumnName(i);
					String columnType = metaData.getColumnTypeName(i);
					table.put(columnName.toUpperCase(), columnType);
				}
				tableField.put(tableName, table);

			} catch (Exception e) {
				LogEngine.getLogger("TABLE-WARN").warn("[table no find]tableName:"+tableName);
				LogEngine.getLogger("TABLE-WARN").error(e.getMessage(), e);
            }finally {
				if(conn!=null){
					try {
						conn.close();
					} catch (Exception ignored) {
					}
				}
				if(stmt!=null){
					try {
						stmt.close();
					} catch (Exception ignored) {
					}
				}
				if(rs!=null){
					try {
						rs.close();
					} catch (Exception ignored) {
					}
				}
			}
			return table;
		}
	}
	
	/**
	 * 判断表字段是否存在
	 * @param tableName
	 * @param columnName
	 * @return
	 */
	public boolean exitTableField(String tableName, String columnName){
		boolean exit = false;
		JSONObject tableInfo = this.getTableInfo(tableName);
		exit = tableInfo.containsKey(columnName.toUpperCase());
		if(!exit){
			LogEngine.getLogger("TABLE-WARN").warn("[column no find]tableName:"+tableName+",columnName:"+columnName);
		}
		return exit;
	}
	
	public EasyRecord checkRecord(EasyRecord record){
		String tableName = record.getTableName();
		for (String columnName : new HashSet<>(record.keySet())) {
			if(!this.exitTableField(tableName, columnName)){
				record.remove(columnName);
			}
		}
		return record;
	}
	
	public JSONObject getSmsObj(String tempId){
		return smsTemps.get(tempId);
	}
	
	
	public String getExtConf(String key){
		return this.extConf.getString(key);
	}
	
	
	public String getRandomOcsName() {
		return randomOcsName;
	}

	public void setRandomOcsName(String randomOcsName) {
		this.randomOcsName = randomOcsName;
		CacheUtil.put("OCS_"+this.entId, randomOcsName,300);
	}

	/**
	 * 是否加密数据库密码
	 * @return
	 */
	public boolean isCryptDBPhone(){
         return "true".equalsIgnoreCase(this.getExtConf("CCBAR_PHONE_CRYPT"));
	}

	/**
	 * 是否加密web解码密码
	 * @return
	 */
	public boolean isCryptWebPhone(){
        return "true".equalsIgnoreCase(this.getExtConf("PHONE_CRYPT_SHOW"));
	}
	/**
	 * 是否按任务批次排序
	 * @return
	 */
	public boolean isTaskBatch(){
        return "true".equalsIgnoreCase(this.getExtConf("IS_TASK_BATCH"));
	}
	
	/**
	 * 获得企业所在的schema
	 * @param entId  企业ID
	 * @param tableName 企业名称
	 * @return
	 */
	public String getTableName(String tableName){

		return this.getSchemaId() + "." + tableName;
	}
	
	public EntContext(String entId){
		this.entId = entId;
	}
	
	
	public String getTaskOcsName(){
		if(StringUtils.isNotBlank(this.getOcsName())) return this.getOcsName();
		return this.getRandomOcsName();
	}
	
	public String getOcsName() {
		if(StringUtils.isBlank(this.ocsName)) return "";
		return ocsName;
	}

	public void setOcsName(String ocsName) {
		this.ocsName = ocsName;
	}

	/**
	 * 初始化企业的相关上下文信息，包括：磐石网关，mars网关和数据库等
	 * @throws Exception
	 */
	private void init() throws Exception{
		this.timer = System.currentTimeMillis();

		//String sql = "select P_ENT_ID,ENT_TYPE,ENT_NAME,ENT_CODE,FEE_CODE from CC_ENT where ENT_ID = ?";
		String sql = "select * from CC_ENT where ENT_ID = ?";
		
		JSONObject entInfo = QueryFactory.getWriteQuery(entId).queryForRow(sql, new Object[]{this.entId},new JSONMapperImpl());
		
		if("3".equals(entInfo.getString("ENT_TYPE"))){
			this.resEntId = entInfo.getString("P_ENT_ID");
		}else{
			this.resEntId = this.entId;
		}
		this.entType = entInfo.getString("ENT_TYPE");
		this.entName = entInfo.getString("ENT_NAME");
		this.entCode = entInfo.getString("ENT_CODE");
		this.feeCode = entInfo.getString("FEE_CODE");
		this.state   = entInfo.getIntValue("ENT_STATE");
		
		String extConfString = entInfo.getString("ENT_EXT_CONF");
		if(StringUtils.isNotBlank(extConfString)){
			this.extConf = JSONObject.parseObject(extConfString);
		}
		if(this.extConf == null) this.extConf = new JSONObject();
		
		sql = "select   t1.PETRA_ID,t1.BPO_CALL_PREFIX,t1.MARS_ID,t2.*,t3.MARS_URL ,t1.SCHEMA_ID    from CC_ENT_RES  t1  , CC_PETRA_RES t2 ,  CC_MARS_RES t3   where  t1.PETRA_ID = t2.PETRA_ID and t1.MARS_ID = t3.MARS_ID  and t1.ENT_ID = ?";
		EasyRow row = QueryFactory.getWriteQuery(entId).queryForRow(sql, new Object[]{this.getResEntId()});
		if(row == null) return;
		this.petraId = row.getColumnValue("PETRA_ID");
		this.marsId = row.getColumnValue("MARS_ID");
		this.petraGwUrl = row.getColumnValue("PETRA_URL")+"/petradatagw/interface";
		this.marsGwUrl = row.getColumnValue("MARS_URL") +"/yc-api/interface";
		this.callbackUrl = row.getColumnValue("MARS_URL") +"/yc-api/callback";
		this.schemaId = row.getColumnValue("SCHEMA_ID");
		this.agentOrderPrefix = row.getColumnValue("AGENT_ORDER_PREFIX");
		this.ocsName  = StringUtils.trimToEmpty(row.getColumnValue("OCS_NAME"));
		this.bpoPrefix = StringUtils.trimToEmpty(row.getColumnValue("BPO_CALL_PREFIX"));
		
		sql = "SELECT VOX_PATH FROM CC_ENT_VOX where VOX_TYPE = 5 and ENT_ID = ?  ";
		this.agentOrderVox = QueryFactory.getWriteQuery(entId).queryForString(sql, new Object[]{this.getResEntId()});
		
		sql = " select BUSI_ORDER_ID,BUSI_ID  from cc_busi_order  where ent_id  = ? ";
		
		List<EasyRow> _orders = QueryFactory.getWriteQuery(entId).queryForList(sql, new Object[]{this.entId});
		for(EasyRow order:_orders){
			this.orders.put(order.getColumnValue("BUSI_ID"), order.getColumnValue("BUSI_ORDER_ID"));
			this.busiOrderIds.put(order.getColumnValue("BUSI_ORDER_ID"), order.getColumnValue("BUSI_ID"));
		}		
		
		try {
			sql = "select * from CC_SMS_CHN  where ENT_ID = ?";
			List<JSONObject>  _smsTemps =  QueryFactory.getWriteQuery(this.resEntId).queryForList(sql, new Object[]{this.resEntId},new JSONMapperImpl());
			for(JSONObject temp:_smsTemps){
				sql = "select * from CC_SMS_TEMP_PARAM  where SMS_CHN_ID = ? ";
				List<JSONObject> params =QueryFactory.getWriteQuery(this.resEntId).queryForList(sql, new Object[]{temp.getString("SMS_CHN_ID")},new JSONMapperImpl());
				temp.put("TEMP_PARAM", params);
				smsTemps.put(temp.getString("SMS_TEMP_ID"),temp);
			}
		} catch (Exception ex) {
			ApiLogger.getLogger().error(ex,ex);
		}
		
		try {
			sql = "select PAY_TYPE from CC_FEE_CONF where ENT_ID = ?";
			this.payType = QueryFactory.getWriteQuery(this.resEntId).queryForString(sql,new Object[]{this.getResEntId()});
			//'付费方式，1 后付费  2 预付费  3 套餐+预付费',
			if("2".equals(this.payType) || "3".equals(this.payType)){
			}else{
				this.payType = "1";
			}
		} catch (Exception ex) {
			ApiLogger.getLogger().error(ex,ex);
		}
		
		
	}
	
	
	public boolean isPrePayType(){
		return  !"1".equals(this.payType);
	}
	
	/**
	 * 根据被叫获取所在组的下一个外显号码
	 * @param groupId
	 * @param called
	 * @return
	 */
	public String getNextCaller(String groupId,String called){
		JSONObject areaObj = HCodeUtil.getAreacode(called);
		List<String> callers = null;
		if(areaObj == null){
			callers = this.prefixs.get(groupId);
		}else{
			if(StringUtils.isBlank(areaObj.getString("area"))){
				callers = this.prefixs.get(groupId);
			}else{
				callers = this.prefixs.get(groupId+"_"+areaObj.getString("area"));
			}
		}
		if(callers == null) return "";
		if(callers.size()==0) return "";
		Random r = new Random();
		return callers.get(r.nextInt(callers.size()));
	}

	
	public String getBusiOrder(String busiId){
		if(StringUtils.isNotBlank(busiId) && this.orders.containsKey(busiId)){
			return this.orders.get(busiId);	
		}
		for (String string : this.orders.keySet()) {
			return this.orders.get(string);
		}
		return "";
	}
	
	/**
	 * 获取业务ID
	 * @param busiOrderId
	 * @return
	 */
	public String getBusiId(String busiOrderId){
		return this.busiOrderIds.get(busiOrderId);
	}
	
	public String getExtConf(String key ,String defaultValue){
		String value = defaultValue;
		if(extConf.containsKey(key)) value =  extConf.getString(key);
		if(StringUtils.isBlank(value)) return defaultValue;
		return value;
		
	}
	
	public String getSchemaId() {
		return schemaId;
	}

	public String getMarsId() {
		return marsId;
	}

	public String getPetraId() {
		return petraId;
	}

	public String getPetraGwUrl(){
		return this.petraGwUrl;
	}
	
	public String getMarsGwUrl(){
		return this.marsGwUrl;
	}


	public String getEntName() {
		return entName;
	}


	public String getResEntId() {
		return resEntId;
	}


	public void setResEntId(String resEntId) {
		this.resEntId = resEntId;
	}


	public String getEntType() {
		return entType;
	}


	public void setEntType(String entType) {
		this.entType = entType;
	}


	public String getEntCode() {
		return entCode;
	}


	public String getFeeCode() {
		return feeCode;
	}


	public String getAgentOrderPrefix() {
		return agentOrderPrefix;
	}


	public String getAgentOrderVox() {
		return agentOrderVox;
	}


	public String getCallbackUrl() {
		return callbackUrl;
	}
	
	public String getBpoPrefix() {
		return bpoPrefix;
	}


	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}
	
	/**
	 * 初始化外显信息
	 */
	private  void initPrefix() {
		
		try {
			String sql = "  select t1.SKILL_GROUP_ID,t1.PREFIX_GROUP_ID,t1.PREFIX_NUM  from "+this.getTableName("CC_SKILL_GROUP")+" t1  wehre  t1.ENT_ID  = ? ";
			List<EasyRow>  rows = QueryFactory.getWriteQuery(entId).queryForList(sql, new Object[]{this.entId});
			
			for(EasyRow row:rows){
				String prefixNum = row.getColumnValue("PREFIX_NUM");
				String groupId = row.getColumnValue("SKILL_GROUP_ID");
				if(StringUtils.isNotBlank(prefixNum)){
					JSONObject areaObj = HCodeUtil.getAreacode(prefixNum);
					String area = "";
					if(areaObj != null){
						area = areaObj.getString("area");
						if(StringUtils.isNotBlank(area)){
							String areaKey = groupId +"_"+area;
							List<String> _prefixs = this.prefixs.get(areaKey);
							if(_prefixs == null) _prefixs = new ArrayList<String>();
							if(!_prefixs.contains(prefixNum)){
								_prefixs.add(prefixNum);
							}
							this.prefixs.put(areaKey, _prefixs);
						}
						String groupKey = groupId;
						List<String> _prefixs = this.prefixs.get(groupKey);
						if(_prefixs == null) _prefixs = new ArrayList<String>();
						if(!_prefixs.contains(prefixNum)){
							_prefixs.add(prefixNum);
						}
						this.prefixs.put(groupKey, _prefixs);
					}
				}
				
				ArrayList<String> groups = new ArrayList<String>();
				String prefixGroup  = row.getColumnValue("PREFIX_GROUP_ID");
				if(StringUtils.isBlank(prefixGroup)) continue;
				if(prefixGroup.indexOf("[")!=-1){
					try {
						JSONArray arr = JSONArray.parseArray(prefixGroup);
						for(int i = 0 ;i<arr.size();i++){
							groups.add(arr.getString(i));
						}
					} catch (Exception ex) {
						ApiLogger.getLogger().error(ex, ex);
					}
				}else{
					groups.add(prefixGroup);
				}
				for(String _groupId:groups){
					sql = " select PREFIX_NUM,AREA_CODE from "+this.getTableName("CC_PREFIX_GROUP_PREFIX")+" t1,CC_PREFIX t2 where t1.PREFIX_NUM = t2.PREFIX_NUM and t1.ENT_ID = t2.ENT_ID  and  t1.PREFIX_GROUP_ID = ? ";
					List<EasyRow>  prefixNums = QueryFactory.getTaskWriteQuery(entId).queryForList(sql, new Object[]{_groupId});
					for (EasyRow _row : prefixNums) {
						prefixNum = _row.getColumnValue("PREFIX_NUM");
						String area = _row.getColumnValue("AREA_CODE");
						if (StringUtils.isNotBlank(prefixNum)) {
							if (StringUtils.isNotBlank(area)) {
								String areaKey = groupId + "_" + area;
								List<String> _prefixs = this.prefixs.get(areaKey);
								if (_prefixs == null)  _prefixs = new ArrayList<String>();
								if (!_prefixs.contains(prefixNum)) {
									_prefixs.add(prefixNum);
								}
								this.prefixs.put(areaKey, _prefixs);
							}

							String groupKey = groupId;
							List<String> _prefixs = this.prefixs.get(groupKey);
							if (_prefixs == null) 	_prefixs = new ArrayList<String>();
							if (!_prefixs.contains(prefixNum)) {
								_prefixs.add(prefixNum);
							}
							this.prefixs.put(groupKey, _prefixs);
						}
					}
				}
			}
		} catch (Exception ex) {
			ApiLogger.getLogger().error("Init prefix error,cause:" + ex.getMessage(), ex);
		}
	}
	
	public int getState() {
		return state;
	}


	public void setState(int state) {
		this.state = state;
	}

	
	/**
	 * 预付费检查
	 * @param entId
	 */
	public boolean  feeCheck() {
		
		//获得当前企业的状态
		//企业认证结果,000 正常  001 告警  401 被叫黑名单 403 主叫黑名单   404 entId参数为空  405 caller参数为空  406 called参数为空  500 缺少企业的计费信息  901 企业被暂停 902 企业余额不足  903 企业欠费
		String result = cache.get("G_ENT_STATE_"+entId);  
		
		if(Constants.isFeeAuthen() && this.isPrePayType()){
			if(StringUtils.isBlank(result)){
				return false;
			}
			
			/**
			 * 判断顺序:1.余额  2.主叫  3.被叫
			 */
			if("000".equals(result) || "001".equals(result)){  // 如果 000 正常  001 告警   ，则再判断主叫/被叫的有效性
				
			}else{
				return false;
			}
		}
		
		return true;
	}
	
	/**
	 * 获取队列名称
	 * @param queueId
	 * @return
	 */
	public String getQueueName(String queueId){
		String queueName = "";
		if(StringUtils.isNotBlank(queueId)){
			if(queueMap.containsKey(queueId)){
				queueName = queueMap.get(queueId);
			}else{
				try {
					queueName = QueryFactory.getTaskWriteQuery(entId).queryForString("SELECT QUEUE_NAME FROM " + getTableName("CC_SKILL_QUEUE") + " WHERE QUEUE_ID = ?", new Object[]{queueId});
				} catch (SQLException ex) {
					ApiLogger.getLogger().error(ex.getMessage(), ex);
				}
				queueMap.put(queueId, queueName);
			}
		}
		return queueName;
	}
	/**
	 * 获取技能组名称
	 * @param groupId
	 * @return
	 */
	public String getGroupName(String groupId){
		String groupName = "";
		if(StringUtils.isNotBlank(groupId)){
			groupName = this.getGroupInfo(groupId).getString("SKILL_GROUP_NAME");
		}
		return groupName;
	}

	public String getGroupCode(String groupId){
		String groupCode = "";
		if(StringUtils.isNotBlank(groupId)){
			groupCode = this.getGroupInfo(groupId).getString("SKILL_GROUP_CODE");
		}
		return groupCode;
	}
	
	private JSONObject getGroupInfo(String groupId){
		JSONObject group = null;
		if(groupMap.containsKey(groupId)){
			group = groupMap.get(groupId);
		}else{
			try {
				group = QueryFactory.getTaskWriteQuery(entId).queryForRow("SELECT SKILL_GROUP_NAME,SKILL_GROUP_CODE FROM " + getTableName("CC_SKILL_GROUP") + " WHERE SKILL_GROUP_ID = ?", new Object[]{groupId}, new JSONMapperImpl());
			} catch (SQLException ex) {
				ApiLogger.getLogger().error(ex.getMessage(), ex);
			}
			if(group == null){
				group = new JSONObject();
			}
			groupMap.put(groupId, group);
		}
		return group;
	}

	
	public String getAgentName(String userAcct){
		String agentName = "";
		if(StringUtils.isNotBlank(userAcct)){
			if(agentMap.containsKey(userAcct)){
				agentName = agentMap.get(userAcct);
			}else{
				try {
					agentName = QueryFactory.getTaskWriteQuery(entId).queryForString("SELECT AGENT_NAME FROM " + getTableName("CC_BUSI_USER") + " WHERE USER_ACCT = ?", new Object[]{userAcct});
				} catch (SQLException ex) {
					ApiLogger.getLogger().error(ex.getMessage(), ex);
				}
				agentMap.put(userAcct, agentName);
			}
		}
		return agentName;
	}
}
