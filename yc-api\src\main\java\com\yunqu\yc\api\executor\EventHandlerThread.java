package com.yunqu.yc.api.executor;

import com.yunqu.yc.api.listener.GlobalContextListener;
import com.yunqu.yc.api.log.EventLogger;
import com.yunqu.yc.api.log.TaskLogger;
import org.apache.log4j.Logger;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;


public class EventHandlerThread implements Runnable{

	private   String sessionId = "";
	private   BlockingQueue<AbstractExecutor> eventQueue = new LinkedBlockingQueue<>(10000);
	private   long threadId = 0 ;
	private   boolean handleState = false;

	// 线程安全释放相关字段
	private volatile long lastActiveTime = System.currentTimeMillis();  // 最后活跃时间
	private  boolean shouldExit = false;

	private  boolean doneFlag = false;//线程是否执行完毕

	public static final long IDLE_TIMEOUT = 120 * 1000;  // 120秒超时
	// 执行时间告警相关字段
	public static final long EXECUTION_WARN_TIMEOUT = 5 * 1000;  // 5秒执行告警阈值
	
	private static Logger logger = EventLogger.getLogger();
	
	public EventHandlerThread(long threadId){
		this.threadId = threadId;
	}
	
	
	/**
	 * 获取线程ID
	 */
	public long getThreadId() {
		return threadId;
	}

	/**
	 * 检查线程是否允许退出
	 * 条件：已设置了应该退出 并且 队列为空
	 */
	private boolean isAllowExit() {
		return isShouldExit() && eventQueue.isEmpty();
	}

	/**
	 * 检查线程是否应该退出
	 * 条件：已设置了应该退出
	 */
	public boolean isShouldExit() {
		return shouldExit;
	}

	/**
	 * 设置线程是否应该退出，只要空闲时长超时120秒就应该退出
	 */
	private void checkShouldExit() {
		if(System.currentTimeMillis() - lastActiveTime > IDLE_TIMEOUT){
			shouldExit = true;
//			logger.warn("EventHandlerThread[" + threadId + "] 检测到线程空闲时间超"+IDLE_TIMEOUT+"ms，设置为：“应该退出”");
		}
	}
	/**
	 * 获取队列大小
	 */
	public int getQueueSize() {
		return eventQueue.size();
	}

	/**
	 * 获取执行器的详细信息，用于告警日志
	 * @param executor 执行器对象
	 * @return 详细信息字符串
	 */
	private String getExecutorDetails(AbstractExecutor executor) {
		if(executor == null) {
			return "null";
		}

		try {
			StringBuilder details = new StringBuilder();
			details.append("类型=").append(executor.getClass().getSimpleName());
			details.append(", 完整类名=").append(executor.getClass().getName());

			// 获取sessionId
			String sessionId = executor.getSessionId();
			if(sessionId != null) {
				details.append(", sessionId=").append(sessionId);
			} else {
				details.append(", sessionId=null");
			}

			// 获取对象hashCode，便于跟踪同一个对象
			details.append(", hashCode=").append(executor.hashCode());

			// 获取toString信息（如果子类重写了toString方法）
			String toStringInfo = executor.toString();
			if(toStringInfo != null && !toStringInfo.equals(executor.getClass().getName() + "@" + Integer.toHexString(executor.hashCode()))) {
				details.append(", toString=").append(toStringInfo);
			}

			return details.toString();
		} catch (Exception ex) {
			return "获取详情失败: " + ex.getMessage();
		}
	}
	
	
	@Override
	public void run() {
		while(GlobalContextListener.runState){
			checkShouldExit();
			try {
				AbstractExecutor executor = eventQueue.poll(100, TimeUnit.MILLISECONDS);
				if(executor != null) {
					// 记录执行开始时间
					long startTime = System.currentTimeMillis();
					logger.debug("EventHandlerThread["+this.threadId+"] EventExecutor.execute() -> "+executor);
					try {
						executor.execute();
					} catch (Exception ex) {
						logger.error("[ERROR]EventHandlerThread.execute() error , cause:"+ex.getMessage(),ex);
					}finally {
						// 计算执行耗时
						long executionTime = System.currentTimeMillis() - startTime;

						// 检查是否超过告警阈值
						if(executionTime > EXECUTION_WARN_TIMEOUT) {
							logger.warn("[WARN]EventHandlerThread[" + this.threadId + "] 消息执行超时告警: " +
								"执行时间=" + executionTime + "ms, " +
								"告警阈值=" + EXECUTION_WARN_TIMEOUT + "ms, " +
								"消息详情=" + getExecutorDetails(executor));
						}

						// 只有在消息执行完成后才更新最后活跃时间
						lastActiveTime = System.currentTimeMillis();
					}
					continue;
				}
			} catch (Exception ex) {
				logger.error("[ERROR]EventHandlerThread.run() error , cause:"+ex.getMessage(),ex);
			}

			// 检查是否可以退出（已设置了应该退出 并且 队列为空）
			if(isAllowExit()) {
//				logger.warn("EventHandlerThread[" + threadId + "] 检测到线程可以退出死循环：已设置了“应该退出” 并且 队列为空！");
				break;
			}

			//是否释放线程（这里要判断队列是否有消息，可能主线程刚好分发了消息进来）
			if(eventQueue.isEmpty() && this.handleState){
				EventDispatcher.removeSession(this.sessionId);
				this.handleState = false;
				// 只有非退出状态的线程才返回线程池
				if(!isShouldExit()) {
					EventDispatcher.addHandler(this,System.currentTimeMillis());
				}
			}

		}

		// 线程退出时的清理工作
		logger.info("EventHandlerThread[" + threadId + "] 线程退出，队列剩余消息数: " + eventQueue.size());
		if(!eventQueue.isEmpty()) {
			logger.warn("EventHandlerThread[" + threadId + "] 退出时队列非空，可能存在消息丢失风险");
		}

		doneFlag = true;
		// 递减线程计数
		EventDispatcher.decrementThreadCount();
	}

	/**
	 * 把事件处理放进线程中进行处理。
	 * @param executor
	 */
	public void addEvent(AbstractExecutor executor){
		// 首次绑定时更新活跃时间，防止线程在处理消息前被误判为超时
		// 这里更新是安全的，因为线程即将开始工作
		lastActiveTime = System.currentTimeMillis();

		try {
			this.sessionId = executor.getSessionId();
			boolean offer = eventQueue.offer(executor, 100, TimeUnit.MILLISECONDS);
			if(!offer){
				logger.warn("EventHandlerThread.addEvent() -> eventQueue.offer()=false : "+executor);
			}
		} catch (Exception ex) {
			logger.error("[ERROR]EventHandlerThread.addEvent() error,cause:"+ex.getMessage(),ex);
		}
		handleState = true;
	}
	
	//append到当前处理的队列中
	public void appendEvent(AbstractExecutor executor) {
		if(eventQueue.size()>2000){
			logger.warn("[WARN]EventHandlerThread.addEvent() 待处理的执行器超过2000，丢弃当前的执行器,Executor->:"+executor);
			return;
		}
		try {
			this.sessionId = executor.getSessionId();
			boolean offer = eventQueue.offer(executor, 100, TimeUnit.MILLISECONDS);
			if(!offer){
				logger.warn("EventHandlerThread.appendEvent() -> eventQueue.offer()=false : "+executor);
			}
		} catch (Exception ex) {
			logger.error("[ERROR]EventHandlerThread.appendEvent() error,cause:"+ex.getMessage(),ex);
		}
	}

	/**
	 * 线程是否执行完毕
	 * @return
	 */
	public boolean isDone() {
		return doneFlag;
	}
}
