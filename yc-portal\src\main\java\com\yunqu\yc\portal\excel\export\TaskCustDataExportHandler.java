package com.yunqu.yc.portal.excel.export;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.excel.BaseRowHandler;
import com.yunqu.yc.portal.utils.PhoneCryptor;
import com.yunqu.yc.portal.utils.TempFiledUtils;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;

public class TaskCustDataExportHandler extends BaseRowHandler {

    private final JSONObject params;
    private final YCUserPrincipal userPrincipal;

    private final List<List<String>> headers = new ArrayList<>();
    private final List<String> columns = new ArrayList<>();

    private JSONObject taskObj;


    public TaskCustDataExportHandler(YCUserPrincipal userPrincipal, JSONObject params) {
        this.params = params;
        this.userPrincipal = userPrincipal;
    }

    @Override
    protected void begin() {
        super.begin();
        taskObj = getTask(params.getString("taskId"));
        getColumns();
    }

    @Override
    protected void end() {
        super.end();
    }

    @Override
    public String getFileName() {
        return getTask(params.getString("taskId")).getString("TASK_NAME")+"_"+ EasyCalendar.newInstance().getDateInt();
    }

    @Override
    public String getTableName(String tableName) {
        return userPrincipal.getSchemaName() + "." + tableName;
    }

    @Override
    public List<List<String>> getHead() {
        return headers;
    }

    @Override
    public EasySQL getSql() {
        List<String> columns = getColumns();
        EasySQL sql = new EasySQL("select t1.TASK_STATE,t1.OBJ_MEMO,t1.SALE_FINISH_TIME,t1.CUST_SATISFY,t1.ORDER_PROD_LIST");
        if (!columns.isEmpty()) {
            sql.append(",t1." + StringUtils.join(columns, ",t1."));
        }
        sql.append("from "+getTableName("cc_task_obj t1"));
        sql.append("where 1=1 ");
        sql.append(params.getString("taskId"), " and t1.TASK_ID = ?",false);
        sql.append(userPrincipal.getEntId(),"and t1.ENT_ID = ?");
        sql.append(userPrincipal.getBusiOrderId(),"and t1.BUSI_ORDER_ID = ?");
        sql.append("and RESULT_ID is null");

        logger.info("exportTaskCustData---> "+sql.toFullSql());

        return sql;
    }

    @Override
    public List<String> parseData(Map<String,String> data) {
        boolean exportCrypt = PhoneCryptor.getInstance().exportCrypt(userPrincipal.getResEntId());
        int taskSource = taskObj.getIntValue("TASK_SOURCE");

        List<String> list = new ArrayList<>();
        int taskState = 0;
        if (StringUtils.isNotBlank(data.get("TASK_STATE"))){
            taskState = Integer.parseInt(data.get("TASK_STATE"));
        }
        for (String key : columns) {
            if(taskSource>1&&key.startsWith("TEL_NUM")&&taskState!=9){
                list.add("******");
            }else{
                if(key.startsWith("TEL_NUM")){
                    list.add(PhoneCryptor.getInstance().decrypt(data.get(key),exportCrypt));
                }else{
                    list.add(data.get(key));
                }
            }
        }

        return list;
    }

    private List<String> getColumns() {
        try {
            int taskSource = taskObj.getIntValue("TASK_SOURCE");
            Map<String, String> tempMap = this.getQuery().queryForRow("select " + TempFiledUtils.fileds(",", userPrincipal.getSchemaName()) + " from " + getTableName("cc_cust_temp") + " where TEMP_ID = ?", new Object[]{taskObj.getString("TEMP_ID")}, new MapRowMapperImpl());
            for (String key : tempMap.keySet()) {
                String jsonStr = tempMap.get(key);
                if (StringUtils.notBlank(jsonStr)) {
                    JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                    String inuse = jsonObject.getString("inuse");
                    if ("1".equals(inuse)) {
                        if (taskSource > 1) {
                            String dataType = jsonObject.getString("dataType");
                            if (!"hide".equals(dataType)) {
                                headers.add(Arrays.asList(jsonObject.getString("name")));
                                columns.add(key);
                            }
                        } else {
                            headers.add(Arrays.asList(jsonObject.getString("name")));
                            columns.add(key);
                        }
                    }
                }
            }
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
        }
        return columns;
    }

}
