package com.yunqu.yc.api.executor;

import com.yunqu.yc.api.base.Constants;
import com.yunqu.yc.api.listener.GlobalContextListener;
import com.yunqu.yc.api.log.EventLogger;
import com.yunqu.yc.api.log.TaskLogger;
import org.apache.log4j.Logger;
import org.easitline.common.utils.string.StringUtils;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;


public class EventDispatcher implements Runnable{
	
	//保持目前mediagw所有的消息处理，消息进入该队列后统一处理。
	private  static BlockingQueue<AbstractExecutor> messageQueue = new LinkedBlockingQueue<>();
	/**
	 * 保存当前线程池的信息，如果线程空闲，则重新进入队列重新排队。
	 */
	private  static BlockingQueue<EventHandlerThread> threadQueue = new LinkedBlockingQueue<>(1000);
	/**
	 * 保存当前session和线程的处理关系。
	 */
	private  static Map<String,EventHandlerThread>  sessionHandlers = new ConcurrentHashMap<>();

	/**
	 * 跟踪所有活跃线程，用于生命周期管理
	 */
	private static final ConcurrentHashMap<Long, EventHandlerThread> activeThreads = new ConcurrentHashMap<>();
	private static ThreadGroup threadGroup = null;

	private static Logger logger = EventLogger.getLogger();

	private static volatile long threadCount = 0;

	private  long    messageCount = 0 ;

	private String defSessionId = "ExecutorDefSessionId";

	// 线程清理相关
	private long lastCleanupTime = System.currentTimeMillis();
	public static final long CLEANUP_INTERVAL = 30 * 1000; // 30秒清理一次

	public EventDispatcher(){
		threadGroup = Constants.getAppThreadGroup();
	}

	/**
	 * 最大的处理线程数
	 * @return
	 */
	private int getThreadCount(){
		return Constants.getEventThreadCount();
	}
	
	private EventHandlerThread newHandlerThread(){
		if(!incrementThreadCount()){
			logger.warn("创建新线程失败，当前已创建线程数："+threadCount+" >= 允许最大处理线程数："+getThreadCount());
			return null;
		}
		EventHandlerThread handlerThread =  new EventHandlerThread(threadCount);

		// 注册到活跃线程列表
		activeThreads.put(threadCount, handlerThread);
		logger.info("创建新的EventHandlerThread[" + threadCount + "]，当前活跃线程数: " + activeThreads.size());

		Thread thread = new Thread(threadGroup,handlerThread,Constants.APP_NAME+"_EventHandlerThread"+threadCount);
		thread.start();
		return handlerThread;
	}

	/**
	 * 线程安全地递减线程计数
	 */
	public static synchronized void decrementThreadCount() {
		if(threadCount > 0) {
			threadCount--;
			logger.info("线程退出，递减线程计数，当前线程数: " + threadCount);
		}
	}

	/**
	 * 线程安全地递增线程计数
	 */
	private static synchronized boolean incrementThreadCount() {
		if(threadCount >= Constants.getEventThreadCount()) {
			return false;
		}
		threadCount++;
		return true;
	}


	/**
	 * 把事件处理到处理队列中
	 * @param executor
	 */
	public static void addEvent(AbstractExecutor executor){
		logger.debug("EventDispatcher[queueSize:"+messageQueue.size()+"] << "+executor);
		if(messageQueue.size()>100000){
			logger.warn("EventDispatcher messageQueue.size() over "+messageQueue.size()+",不处理Executor。");
			return;
		}
		//System.out.println("EventHandler.addEvent(),queueSize:"+messageQueue.size()+",sessionId->"+eventObject.getSessionId());
		try {
			boolean offer = messageQueue.offer(executor, 50, TimeUnit.MICROSECONDS);

			if(!offer){
				logger.warn("EventDispatcher.addEvent() messageQueue.offer() false!");
			}
		} catch (Exception ex) {
			logger.error(ex.getMessage(),ex);
		}

	}
	
	/**
	 * 把线程返回到消息处理队列
	 * @param handler
	 */
	public static boolean addHandler(EventHandlerThread handler,long timer){
		// 检查线程是否应该退出，如果是则不返回线程池
		if(handler.isShouldExit()) {
			logger.warn("EventHandlerThread[" + handler.getThreadId() + "] 线程已被设置“应该退出”，不返回线程池");
			return false;
		}

		boolean result = false;
		try {
			result = threadQueue.offer(handler,50,TimeUnit.MILLISECONDS);
			if(!result){
				logger.warn("[ERROR]EventDispather.addHandler() threadQueue.offer() false !");
			}
		} catch (Exception ex) {
			logger.error(ex.getMessage(),ex);
		}
		return result;
	}

	/**
	 * 定期清理已退出的线程
	 */
	private void cleanupExitedThreads() {
		int sizeBefore = activeThreads.size();
		activeThreads.entrySet().removeIf(entry -> {
			EventHandlerThread thread = entry.getValue();
			if (thread.isDone()) {
				logger.debug("清理退出线程: EventHandlerThread[" + thread.getThreadId() + "]，线程消息队列剩余: " + thread.getQueueSize());
				threadQueue.remove(thread);
				return true;
			}
			return false;
		});

		int cleanedCount = sizeBefore - activeThreads.size();
		if(cleanedCount > 0) {
			logger.info("本次清理了 " + cleanedCount + " 个退出线程，当前活跃线程数: " + activeThreads.size() + "，当前线程计数: " + threadCount);
		}
	}

	/**
	 * 获取线程池状态信息
	 */
	public static String getThreadPoolStatus() {
		return String.format("活跃线程数: %d, 空闲线程数: %d, 会话绑定数: %d",
			activeThreads.size(), threadQueue.size(), sessionHandlers.size());
	}
	
	/**
	 * 移除当前处理正在处理的sessionId
	 * @param sessionId
	 */
	public static void   removeSession(String sessionId){
		try {
			sessionHandlers.remove(sessionId);
			//增加保护为了防止内存泄漏
			if(sessionHandlers.size()>10000) sessionHandlers.clear();
		} catch (Exception ex) {
			logger.error("[ERROR]EventDispather  sessionHandlers.remove("+sessionId+") error,cause:"+ex.getMessage(),ex);
		}
	}

	private void printLog(){
		messageCount++;
		if(messageCount > Integer.MAX_VALUE) messageCount = 1;
		if(messageCount%200==0) {
			logger.info("[DEBUG] EventDispatcher 当前处理信息->已处理消息量："+messageCount+",已创建线程数："+threadCount+",线程组活动线程数："+ threadGroup.activeCount()+",队列线程数："+threadQueue.size()+",活跃线程数："+activeThreads.size()+",待处理消息数:"+messageQueue.size());
		}
		//防止待处理消息数非常大时，线程长时间占用CPU资源
		if(messageCount%1000==0) {
			try {
				Thread.sleep(20);
			} catch (Exception e) {
			}
		}
	}

	@Override
	public void run() {
		while(GlobalContextListener.runState){
			try {
				// 定期清理退出的线程
				if(System.currentTimeMillis() - lastCleanupTime > CLEANUP_INTERVAL) {
					cleanupExitedThreads();
					lastCleanupTime = System.currentTimeMillis();
				}

				// 先用peek查看消息，不出队列
				AbstractExecutor executor = messageQueue.peek();
				if(executor == null) {
					Thread.sleep(50);
					continue;
				}

				String sessionId = executor.getSessionId();
				if(StringUtils.isBlank(sessionId)){
					sessionId = defSessionId;
				}

				EventHandlerThread targetHandler = null;

				//这里先判断一下，sessionId在那个handler里面，如果在则用回原来的正在处理的线程。
				EventHandlerThread handler = sessionHandlers.get(sessionId);
				if(handler!=null) {
					// 检查线程是否已标记为退出
					if(handler.isShouldExit()) {
						logger.warn("SessionId[" + sessionId + "] 绑定的线程["+handler.getThreadId()+"]已退出，移除绑定关系");
						sessionHandlers.remove(sessionId);
						handler = null;
					} else {
						targetHandler = handler;
					}
				}

				// 如果没有绑定线程或绑定的线程已退出，从空闲线程中找一个
				if(targetHandler == null) {
					EventHandlerThread idleThread = this.getIdleHandler();
					if(idleThread == null){
						Thread.sleep(50);
						continue; // 没有可用线程，消息继续留在队列中
					}
					targetHandler = idleThread;
				}

				// 确定有可用线程后，才真正从队列中取出消息
				executor = messageQueue.poll(50, TimeUnit.MILLISECONDS);
				if(executor == null) {
					// 这种情况很少见，可能是其他线程取走了消息
					logger.warn("消息在peek和poll之间被其他线程取走");
					continue;
				}

				// 分配消息给目标线程前，最后一次检查线程状态
				if(targetHandler.isShouldExit()) {
					logger.warn("目标线程["+targetHandler.getThreadId()+"]在分配消息前已退出，消息重新入队: " + executor);
					// 将消息重新放回队列头部
					try {
						messageQueue.offer(executor, 10, TimeUnit.MILLISECONDS);
					} catch (Exception ex) {
						logger.error("消息重新入队失败: " + ex.getMessage(), ex);
					}
					continue;
				}

				// 分配消息给目标线程
				if(targetHandler == handler) {
					// 使用已绑定的线程
					targetHandler.appendEvent(executor);
				} else {
					// 使用新分配的线程，建立绑定关系
					sessionHandlers.put(sessionId, targetHandler);
					targetHandler.addEvent(executor);
				}

				printLog();
			} catch (Exception ex) {
				logger.error(ex,ex);
				try {
					Thread.sleep(200);
				} catch (Exception ex1) {
				}
			}
		}
	}
	
	private EventHandlerThread getIdleHandler(){
		try {
			// 尝试从线程池中获取一个线程
			EventHandlerThread eventThread = threadQueue.poll(50, TimeUnit.MILLISECONDS);

			if(eventThread == null) {
				// 没有可用线程，尝试创建新线程
				logger.info("从空闲线程池中获取不到线程，尝试创建新线程....");
				eventThread = this.newHandlerThread();
			}

			if(eventThread == null) {
				return null;
			}

			// 检查获取到的线程是否标记为退出
			if(eventThread.isShouldExit()) {
				logger.info("从线程池中获取到正在退出的线程，返回null让主线程重试: EventHandlerThread[" + eventThread.getThreadId() + "]");
				return null; // 返回null，让主线程while循环重试
			}

			return eventThread;
		} catch (Exception ex) {
			logger.error("[ERROR]EventDispatcher HandlerThread.getIdleHandler() error,cause:"+ex.getMessage());
			logger.error(ex.getMessage(),ex);
		}
		return null;
	}
}
