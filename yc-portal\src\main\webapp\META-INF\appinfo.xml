<?xml version="1.0" encoding="UTF-8"?>
<application id="yc-portal" name="云电销标准版" resTable="ycBusiRes#002"  version="3.4.0#20250117-1" >

    <datasources>
        <datasource name="yc-wirte-ds-1" description="云呼平台数据源(写1)" isnull="true" />
        <datasource name="yc-wirte-ds-2" description="云呼平台数据源(写2)" isnull="true" />
        <datasource name="yc-read-ds" description="云呼平台数据源(读)" isnull="true" />
    </datasources>
    <description>
        3.4.0#20250117-1
            1、自动外呼新增拓展坐席、拓展技能组
        3.4.0#20241031-1
            1、添加审核任务,2、添加提示快要失效
        3.4.0#20241023-1
            1、机器人通话录音导出支持批量导出，优化导出性能
    	3.4.0#20240911-1
    		1、添加订购将要过期提醒功能；
    	3.4.0#20240723-1
    		1、添加批次管理菜单，根据批次审批导入，使用新的菜单和脚本
    		2、处理号码加密问题；
    	3.4.0#20240122-1
    		1、把页面加密上加一个导出加密PHONE_CRYPT_EXPOR，如果配置用页面加密；
    		2、修改首页路径配置方式welcomeUrl；
    	3.4.0#20240117-1
    		1、添加配置【REMOVE_TELNUM_0】，导入名单时是否需要去0，默认需要；
    	3.4.0#20240108-1
    		1、机器人详情没有告警信息报错问题；
        3.4.0#20231212-1
            1.优化文件上传，增加校验文件类型是否在白名单中。
            2.Webcontent/template目录加入到登录校验中。
    	3.4.0#20231207-1
    		1、工单富文本加解密优化；
    		2、处理营销结果查询时显示机器人结果问题；
    		3、处理工单导出内容格式化问题；
    	3.4.0#20231206-1
    		1、处理导入写入redis缓存，没有序列化的问题；
        3.4.0#20231022-1
            1、增加支持上下文跳转。
    	3.4.0#20231007-1
    		1、新增ivr语音通知；
        3.4.0#20230921-1
            1、客户名单导入新增导入顺序随机
        3.4.0#20230816-1
            1、修复机器人提参统计导出无汇总的问题
        3.4.0#20230802-1
            1、修复机器人提参配置页面显示问题
            2、隐藏机器人提参统计分值统计
        3.4.0#20230728-1
            1、修复机器人任务统计通话总时长为空的问题
            2、修复机器人审核版导入客户资料报错问题
        3.4.0#20230725-1
            1、修改任务统计报表导出模板头
            2、机器人任务列表任务名称新增悬浮显示
            3、修复话术新增失败的问题
            4、修复机器人提参统计日期显示问题
            5、修复机器人结果查询导出问题
        3.4.0#20230724-1
            1、优化任务执行页面历史联系记录中号码未加密显示的问题。
    	3.4.0#20230720-1
    		1、由于漏洞扫描处理后，导致技能组选择出现问题，迁移过来yc-portal;
        3.3.0#20230612-1
            1、新增机器人任务名单查询页面
        3.3.0#20230609-1
            1.新增 "自动外呼最大呼叫比"配置，设置之后，外呼任务编辑页面中的“呼叫比”不能大于该配置。
            2.坐席任务分配时，增加task_id。
            3.导入任务名单时，上传名单文件后，模板字段不匹配时，立即返回异常提示。
    	3.3.0#20230524-1
    		1、新增黑名单管理界面；添加cc_task_black_list表CREATOR、CREATE_TIME字段；
    	3.3.0#20230512-1
            1.任务营销结果查询页面，列表页面的客户名称和客户号码 加密显示逻辑，默认读取企业管理-企业拓展配置，选择任务后则使用任务客户资料模板字段配置。
    	3.3.0#20230403-1
    		1、机器人添加复制创建任务功能；
    		2、任务工作时间添加控制节假日；
    	3.3.0#20230317-1
    		1、添加湖北建行机器人呼叫结果统计；
    	3.3.0#20230229-1
    		1、修改任务名单分配，去除事务；
    	3.3.0#20230228-1
    		1、修改导入客户资料按表头导入逻辑；
    	3.3.0#20230215-1
    		1、兼容个性化需求；机器人呼叫结果绑定坐席”坐席需要打标签；使用SALES_CODE;客户资料需要有标签可以跟坐席匹配；使用ext拓展配置;机器人呼叫结果查询，坐席只能查询到符合自己标签的信息“；
       3.3.0#20230310-1
            	修改saas产品化审核任务发送短信区分企业
        3.3.0#20230223-1
            1、优化大数据活动取数逻辑。
        3.3.0#20230217-1
            1、机器人任务机器人外呼名单列表提参查询条件展开显示
        3.3.0#20230207-2
            1、新增机器人提参配置隐藏
        3.3.0#20230207-1
            1、新增前端外呼名单数据加密显示
    	3.3.0#20230203-1
    		1、添加机器人敏感词告警；
        3.3.0#20230130-1
            1.优化防范CSRF攻击过滤器，解决一个用户同时在不同浏览器登录时会出现token校验失败的问题。
    	3.3.0#20230111-1
    		1、修改任务统计；2、优化自动任务查询信息sql;
    	3.3.0#20230103-1
    		1、修改机器人参数配置，改未通用
        3.3.0#20221222-1
            1、名单导入功能优化
        3.3.0#20221126-1
            1.新增 所有后台接口都要做CSRF漏洞防范，包括：window.location=url,window.open(url)
        3.3.0#20221124-1
            1.新增 所有后台接口都要做CSRF漏洞防范，包括：（/webcall，/servlet ）
    	3.3.0#20221122-1
    		1、去除删除页面操作数据
         3.3.0#20221110-1
            1.新增 发起外呼时，显示的号码需要根据企业配置是否加密显示。
            2.优化营销结果查询页面，号码加密显示的问题，如果未选择任务，则判断企业扩展配置是否配置了“页面号码加密显示”
    	3.3.0#20221009-2
    	    1、修改机器人任务已使用名单数不准问题，jsRender不会强制转换
        3.3.0#20220926-1
         1、增加扩展XXS过滤器。
    	3.3.0#20220924-2
    	1、调整营销结果修改逻辑，允许备注填空；2、优化菜单拦截；3、修复兼容挂载到云客服的问题；
        3.3.0#20220914-2
        1、新增配置项是否在机器人报表中展示呼叫被拦截的名单 showRobotIntercept 0:隐藏,1:显示 默认隐藏;
        3.3.0#20220914-1
        1、自动任务、机器人任务添加呼叫模式 videoCall ：1 是视频外呼，0 是语音外呼;
        3.3.0#20220913-1
        1、修改任务黑名单导入；
        3.3.0#20220909-1
        1、兼容postgres数据库
        3.3.0#20220907-1
        1、兼容postgres数据库，修改Integer.getInteger()为Integer.valueOf()或Integer.parseInt()
        3.3.0#20220824-1
        新版
        1、修改机器人任务接通率方式；
        2、修改回访结果通话时长跟通话记录一致；
        3、修改自动任务点查询后出现执行状态；
        4、修改话务管理权限控制；
        5、修改菜单权限控制；
        6、修改话术编码；
        7、修改话务内容，进行编解码；
        8、修改工单类型内容，进行编解码；
        9、新增删除任务数据功能；
        10、新增问卷执行页面展示方式，条件配置【问卷执行方式/wjExcType】
        11、整理营销结果；
        3.2.16#20220823-1
        1、增加区分机器人类型配置项、并对机器人任务进行适配
        3.2.16#20220817-1
        1、兼容postgres数据库。
        2、删除敏感词相关逻辑代码，yc-base中已有敏感词相关逻辑代码。
        3.2.16#20220812-1
        1、坐席预约列表显示坐席信息；
        2、修改菜单越权拦截方式；
        3.2.15#20220810
        1、优化录音文件读取的逻辑，增加录音文件路径拓展，兼容多台录音文件服务器。
        3.2.15#20220728-1
        1、修改分配到坐席查询条件；
        2、修补导入数据为空时没有消息提示；
        3、添加企业拓展配置信息到前端，通过getEntExtConfig(key)获取；
        3.2.14#20220628-1
        1、修改任务名单显示数据
        3.2.13#20220628-1
        2、数据导入时控制不能提交空、不能导入除excle其他格式
        3、导入数据保存路径可以配置；
        4、修改自动任务和机器人任务编辑，添加iptGourp配置；
        3.2.12#20220628-1
        1、修改执行回访任务；
        2、修改预约通知；
        3.2.11#20220623-1
        1.增加应用配置“转IVR订购人工确认”，用于控制任务外呼执行页面的“转IVR订购人工确认”按钮是否显示。
        3.2.11#20220617-2
        1.将saas产品化机器人任务 的时间配置查询移到另一个方法中
        3.2.11#20220617-1
        1、启用页面拦截，在yc-base控制启用；
        2、兼容问卷预约；
        3.2.10#20220616-1
        新增定时任务配置项IS_START_PUSH N:关,Y:开
        3.2.10#20220530-1
        移除应用配置“portal中的jsp页面 token校验开关”，改为读取yc-base中的配置
        3.2.10#20220523-2
        机器人报表中针对审核任务开放已拦截报表;
        接通时增加全局event对象、挂断时全局event赋null;
        3.2.10#20220513-2
        修改来电弹屏失败bug;
        3.2.9#20220512-2
        修改机器人任务审核版的工作时间配置，当配置的时间在9059配置的时间内的话就允许突破工作时间
        3.2.9#20220512-1
        1、修改登录后logo可支持以企业维度获取yc-res项目里面logo，达到不同企业可展示不同logo需求；
        3.2.9#20220509-1
        1、添加机器人参数同步，在修改机器人任务配置时触发，需要先配置请求同步地址；
        3.2.8#20220507-1
        1、新加配置【SaveCustUploadData：保存上传的客户资料】，把上传的客户资料保存到“/easyserver/temp/uploadexcel”目录下
        2、修改自动任务来电弹屏，如果有问卷关联到问卷页面；
        3.2.7#20220428-1
        1、解决机器人结果查询从“未影响”切换回“已营销”时没有数据的问题；
        2、修改呼叫类型问题；
        3、解决ng控制响应时间后，导入所花时间过长而断开没有响应结果的情况，改为监控响应结果。
        3.2.4#20220424-1 修改后台配置向配置菜单默认不选择修改文件【ivr-task-edit.jsp】【audit/ivr-task-edit.jsp】
        3.2.4#20220422-1 机器人结果页面新增已拦截名单查询
        3.2.4#20220421-1
        1.修改将机器人任务（审核版）导入时如果数据不匹配所有审核级别返回提示！
        3.2.3#20220419-1
        解决营销结果查询页面无法导出营销完成的录音
        3.2.3#20220411-1
        1.对所有action以add,update,delete开头的请求增加token校验，用于防范CSRF攻击，可在应用配置中关闭。
        3.2.4#20220419-1
        1.修改将机器人任务（审核版）不需要选择审核级别 根据导入的数据判断是那一级别的。
        3.2.6#20220418-1
        1、当用户没有分配技能组时，话务条不显示
        3.2.6#20220415-1
        1、优化任务数据导入数据慢问题，给敏感词加配置，默认不开启，启用方式：先在【yc-portal】参数配置【任务拓展配置
        】添加【task:3,key:checkSensitiveWord,name:敏感词校验,type:radio,option:1_启动|0_不启用】
        2、给机器人提参加配置，默认不开启，启用方式：先在【yc-portal】参数配置【任务拓展配置
        】添加【task:3,key:confRobotParam,name:提参配置,type:radio,option:1_启动|0_不启用】
        3、修改机器人参数统计表问题；
        4、根据任务配置的callType类型来告诉OCS采用哪种呼叫方式，callType取值，1：先呼叫用户 2 先接通坐席 3 启动IVR流程
        5、修改上传效率，改用批次提交
        3.2.5#20220414-1
        1、修改数据集群时，导入任务数据使用事务提交出现的问题；
        3.2.4#20220412-2
        1.修改将机器人任务（审核版）的资源id加入代码中控制资源权限的@PreAuthorize(resId = {})这个中 修改文件【TaskDao.java】【TaskObjDao.java】【TaskObjRobotDao.java】
        【BlackListServlet.java】【CustomerServlet.java】【ExportServlet.java】【ImportTaskObjServlet.java】【TaskSaleResultServlet.java】【TaskServlet.java】
        3.2.4#20220412-1
        1、机器人新增来电名片、挂机短信配置；
        2、兼容机器人录音wav类型；
        3、修改启动流程配置；
        3.2.3#20220411-1
        1.修改机器人任务（审核版）不能修改任务bug
        2.修改机器人任务（审核版）导入数据时出错问题
        3.2.3#20220406-1
        1.优化我的待分配任务，班长用户无法看到待分配的任务数据，原因：遗漏了个菜单权限。
        3.2.3#20220401-1
        1.修改机器人任务（审核版）创建新任务一直提示“请选择技能组”bug修改文件【/audit/ivr-task-edit.jsp】
        2.修改日志中一直刷的错误sql修改文件【AgentDao.java】
        3.2.3#20220330-2
        1.增加机器人外呼任务(审核版)中状态为审核不通过的提示,修改界面【/audit/ivr-task-list.jsp】、【TaskDao.java】
        3.2.3#20220330-1
        1.修改营销结果界面不选择任务就不能查询的问题
        3.2.2#20220329-1
        1.改造营销结果导出逻辑，解决升级easitline-db-3.2.0.jar后最多只能导出20000条数据限制。
        3.2.2#20220328-1
        1.优化营销结果查询页面，解决导出未营销客户资料功能无数据的问题。
        3.2.2#20220326-1
        1.调整营销结果查询页面数据加载逻辑，延长数据加载图标展示时间。
        2.优化营销结果相关SQL
        3.2.2#20220325-1
        1.自动外呼任务编辑页面“呼损率“改回 ”呼叫速率因子“。
        2.优化营销结果查询SQL，解决 勾选了“成功”，“失败”，”待录入“后导出营销结果失败的问题。
        3.2.2#20220324-3
        1.后台接口新增菜单权限校验，Mars相关jar也要升级：easitline-core-3.2.0.jar，easitline-db-3.2.0.jar，easitline-utils-3.2.0.jar。
        3.2.2#20220324-2 1、修改营销结果被改动后没有客户资料的问题；2、调整任务统计报表，新增接通名单率和营销成功率；
        3.2.2#20220324-1 1、修改客户资料-模板管理-添加或者修改模板，默认 客户电话1 字段不允许编辑，避免客户把字段修改后，导入提示无号码等问题
        3.2.2#20220316-1 1、修改机器人播放录音的参数
        3.2.2#20220315-2
        1、任务执行页增加“转IVR订购人工确认” 按钮，新增字段：ALTER TABLE cc_task_obj ADD IVR_SALE_SELECT_KEY VARCHAR(20) COMMENT '发起IVR订购时，保存用户输入的按键值';
        2、引入ccbar.js增加时间戳。
        3.2.2#20220315-1 1、修改云机器人任务中新增、修改 弹出框修改成自适应宽度 修改文件【ivr-task-list.jsp】
        3.2.2#20220313-1 1、修改机器人任务技能组问题，3、修改机器人结果查询问题
        3.2.1#20220308-1 1、修改机器人任务时间选择问题
        3.2.1#20220307-2 1、修改机器人任务配置；2、应欧普要求在task-cust-list.jsp添加导入时间查询条件
        3.2.1#20220307-2 1、预约任务提示
        3.2.1#20220307-2 1、修改机器人任务配置
        3.2.0#20220307-1 1、任务营销结果，导出，问卷详情优化
        3.2.0#20220211-1 1、发版
        2.5.5#20220104-1 1、机器人任务添加配置信息，添加机器人任务配置项
        2.5.5#20211228-1 1、优化任务组管理统计(groupTaskStat()),执行任务里往来通话记录
        2.5.5#20211221-1 1、二次营销添加坐席姓名和坐席工号作为筛选条件,添加ocs外呼时间配置
        2.5.5#20211215-1 1、优化机器人任务审核；应用业务名称配置项，把系统页面上所有营销字眼替换成配置项中配置的信息描述；  2、资源菜单整理
        2.5.5#20211209-1 1、webcall权限控制；
        2.5.5#20211125-1 1、新增机器人审核；2、导入名单敏感词过滤；
        2.5.4#20211123-1 优化 任务执行页面中编辑客户资料（my-task-execute.jsp），手机号码需要做“页面加密”显示，提交时不能提交“页面加密”内容；
        2.5.4#20211110-1 修改任务导入名单号码匹配规则；
        2.5.4#20211026-1 1、修改坐席执行最后一条不会关闭tab业务问题；2、修改回访结果导出获取任务名称报错问题；3、任务统计报表的日期维度存在歧义，单独列出来；4、新增配置【extConfTask】，适配不同平台不同的拓展字段，根据配置信息会在任务编辑页面呈现；
        2.5.3#20210930-1 1、增加自动任务、机器人任务单独设置工作时间的配置，可以设定启用不启用。 2、增加自动任务导入导出的失败记录数。
        2.5.3#20210903-1 湖南电信电渠外呼平台增加导入号码去重功能
        2.5.3#20210624-1  优化任务统计报表sql
    </description>
    <versionHistory>

    </versionHistory>
</application>
