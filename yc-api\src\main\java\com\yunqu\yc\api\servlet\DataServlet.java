package com.yunqu.yc.api.servlet;

import java.io.IOException;


import java.io.PrintWriter;
import java.sql.SQLException;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.render.ContentType;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.base.QueryFactory;

/**
 * 处理业务回调的结果,格式： url?data={result:result,userData:userData} 满意度：1 非常满意 2 满意 3 一般
 * 4 不满意
 * 
 * <AUTHOR>
 *@WebServlet("/data")
 */

public class DataServlet extends HttpServlet {

	private static final long serialVersionUID = 4313271959495941176L;
	
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException,IOException{
		this.doPost(request, response);
	}
	
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException,IOException {
		if(request.getUserPrincipal()==null) {
			try {
				response.getWriter().println("illegal request");
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			return;
		}
		
		/**
		 * 区县政府
		      公共服务行业
		      事业单位
		      市直部门
		      其他
		 */
		try {
			addGroup(2,"0501",501000);
//			addGroup(2,"0501",501002,"公共服务行业","20");
//			addGroup(2,"0501",501003,"事业单位","30");
//			addGroup(2,"0501",501004,"市直部门","40");
//			addGroup(2,"0501",501005,"其他","90");
		} catch (Exception ex) {
			response.getWriter().println(ex);
			ex.printStackTrace();
		}
	
		//addGroup("501","区县");
	}
	
	/**
	 * 
	 * @param pGroupId 父技能组ID
	 * @param groupType  技能组类型，0501 二级承办单位
	 * @param groupCode  技能组编码
	 * @param orgClass  二级承办单位分类  10   
	 * @throws Exception
	 */
	public static void addGroup(long pGroupId, String  groupType,long groupCode)  throws Exception{
		EasyQuery query = QueryFactory.getWriteQuery("12345");
			List<JSONObject> list = query.queryForList("select * from ycbusi_ekf.sy12345_dept where id like '1\\_%'    order by id ", new Object[]{},new JSONMapperImpl());
			int i = 0 ;
			for(JSONObject obj:list){
				groupCode++; //501001
				String orgCalss = "0";
				String level = obj.getString("LEVEL");
				if("区县政府".equals(level))  orgCalss = "10";
				if("公共服务行业".equals(level))  orgCalss = "20";
				if("事业单位".equals(level))  orgCalss = "30";
				if("市直部门".equals(level))  orgCalss = "40";
				if("其他".equals(level))  orgCalss = "90";
				addGroup(query,obj,pGroupId,groupType,groupCode,orgCalss,i++);
			}
	}
   /**
    * 
    * @param query
    * @param groupObj 
    * @param pGrouId 父节点ID
    * @param groupCode  当前节点代码
    * @param groupType  节点类型，0501,050101
    * @param idx
    * @throws Exception
    */
	public static void addGroup(EasyQuery query,JSONObject groupObj,long pGrouId,String groupType,long groupCode,String orgClass,int idx) throws Exception{
			String level = groupObj.getString("LEVEL");
			
			String busiOrderId = "83333019177779697379290";
			String entId =  "12345";
			
			int groupId = getSkillGroupId(query);
			EasyRecord record = new EasyRecord("ycbusi_ekf.cc_skill_group");
			record.set("SKILL_GROUP_ID", groupId);
			record.set("SKILL_GROUP_CODE", groupCode);
			record.set("GROUP_TYPE", groupType);
			record.set("BUSI_ORDER_ID",busiOrderId );
			record.set("ENT_ID",entId);
			record.set("SKILL_GROUP_NAME", groupObj.getString("NAME"));
			record.set("SKILL_GROUP_TYPE", "struct");
			record.set("P_GROUP_ID", pGrouId);
			record.set("IDX_ORDER", idx);
			record.set("ORG_CLASS", orgClass);
			query.save(record);
			
			String[] items = StringUtils.split(groupObj.getString("ID"),"_");
			
			createNewUser(query,items[1],groupId,groupObj.getString("NAME"),groupCode,entId,busiOrderId);
			//System.out.println(groupObj.getString("ID")+",size:"+items.length);
			
			List<JSONObject> list = query.queryForList("select * from ycbusi_ekf.sy12345_dept where id like '"+items[1]+"\\_%' order by id  ", new Object[]{},new JSONMapperImpl());
			int i = 0 ;
			long _groupCode = groupCode*1000;
			for(JSONObject obj:list){
				_groupCode++;
				addGroup(query,obj,groupId,groupType+"01",_groupCode,"0",i++);
			}
		}
		
		public static int getSkillGroupId(EasyQuery query) throws SQLException{
			String sql = "SELECT SEQ_ID FROM cc_seq where table_id = 'CC_SKILL_GROUP' ";
			int id = query.queryForInt(sql, new Object[]{});
			id++;
			sql = "update ycmain.cc_seq set SEQ_ID = "+id+" where  table_id = 'CC_SKILL_GROUP' ";
			query.executeUpdate(sql, new Object[]{});
			return id;
		}

		public static void createNewUser(EasyQuery query,String depId,int groupId,String deptName,long groupCode,String entId,String busiOrderId) throws Exception{
			
			String sql = "select userName,accountName,tel,sex  from ycbusi_ekf.sy12345_dept_user where depId = '"+depId+"'  and  accountName  like '1%'";
			List<JSONObject> users = query.queryForList(sql, new Object[]{},new JSONMapperImpl());
			
			for(JSONObject user:users){
				
				sql = "select count(1) from CC_USER where USER_ACCT = '"+user.getString("ACCOUNTNAME")+"'";
				if(query.queryForExist(sql, new Object[]{})){
					continue;
				}
				
				String userId = RandomKit.uniqueStr();
				String passWord = "sy123456";	//超级管理员密码
				EasyRecord record = new EasyRecord("CC_USER", "USER_ID");
				record.setPrimaryValues(userId);
				record.set("ENT_ID", entId);
				record.set("USERNAME", user.getString("USERNAME"));
				record.set("USER_ACCT", user.getString("ACCOUNTNAME"));
				record.set("AGENT_PHONE", "");
				record.set("ADMIN_FLAG", 0);
				record.set("LOCK_STATE", 0);
				record.set("USER_STATE", 0);
//				record.set("DEPT_NAME", deptName);
//				record.set("DEPT_CODE", groupCode);
				record.set("USER_PWD", MD5Util.getHexMD5(passWord));
				record.set("CREATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
				record.set("CREATOR", "初始化数据");
				record.set("MOBILE", user.getString("ACCOUNTNAME"));
				query.save(record);
				
				String roleId = "521-12345";
				
				EasyRecord record1 = new EasyRecord("ycbusi_ekf.CC_BUSI_USER");
				record1.set("USER_ID", userId);
				record1.set("BUSI_ORDER_ID", busiOrderId);
				record1.set("ENT_ID", entId);
				record1.set("ROLE_ID", roleId);
				record1.set("USER_STATE", 0);
				record1.set("AGENT_NAME", user.getString("USERNAME"));
				record1.set("USER_ACCT", user.getString("ACCOUNTNAME"));
				record1.set("ADMIN_FLAG", 0);
				record1.set("STRUCT_LIST", deptName);
				record1.set("ROLE_LIST", "承办单位承办人");
				query.save(record1);
				
				EasyRecord record2 = new EasyRecord("ycbusi_ekf.CC_SKILL_GROUP_USER");
				record2.set("SKILL_GROUP_ID", groupId);
				record2.set("USER_ID", userId);
				record2.set("BUSI_ORDER_ID", busiOrderId);
				record2.set("ENT_ID", entId);
				record2.set("IDX_ORDER", 0);
				query.save(record2);
				
				EasyRecord record3 = new EasyRecord("ycbusi_ekf.CC_ROLE_USER");
				record3.set("ROLE_ID", roleId);
				record3.set("USER_ID", userId);
				record3.set("BUSI_ORDER_ID", busiOrderId);
				record3.set("ENT_ID", entId);
				record3.set("CREATOR", "初始化数据");
				query.save(record3);
				
			}
			
		}
		
	
	private void write(HttpServletResponse response,Object obj){
		PrintWriter writer = null;
		try {
			response.setHeader("Pragma", "no-cache");	// HTTP/1.0 caches might not implement Cache-Control and might only implement Pragma: no-cache
			response.setHeader("Cache-Control", "no-cache");
			response.setDateHeader("Expires", 0);
			response.setContentType(ContentType.TEXT.value());
			response.setCharacterEncoding("UTF-8");	// 与 contentType 分开设置
			writer = response.getWriter();
			writer.write(obj.toString());
			writer.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
}
