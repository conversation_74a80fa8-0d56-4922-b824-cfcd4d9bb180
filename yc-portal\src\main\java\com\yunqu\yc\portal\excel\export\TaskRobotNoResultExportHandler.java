package com.yunqu.yc.portal.excel.export;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.excel.BaseRowHandler;
import com.yunqu.yc.portal.utils.PhoneCryptor;
import com.yunqu.yc.portal.utils.TempFiledUtils;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;

public class TaskRobotNoResultExportHandler extends BaseRowHandler {

    private final JSONObject params;
    private final YCUserPrincipal userPrincipal;
    private final Map<String,String> dataTypeMap = new HashMap<>();
    private boolean num = false;
    private final List<String> headers = new ArrayList<>();
    private final List<String> columns = new ArrayList<>();

    public TaskRobotNoResultExportHandler(YCUserPrincipal userPrincipal, JSONObject params) {
        this.params = params;
        this.userPrincipal = userPrincipal;
    }

    @Override
    protected void begin() {
        super.begin();
        getColumns();
    }

    @Override
    public String getFileName() {
        return "机器人名单列表";
    }

    @Override
    public String getTableName(String tableName) {
        return userPrincipal.getSchemaName()+"."+tableName;
    }

    @Override
    public List<List<String>> getHead() {
        List<List<String>> head = new ArrayList<>();
        head.add(Arrays.asList("客户名称"));
        head.add(Arrays.asList("客户电话"));
        head.add(Arrays.asList("所属任务"));

        //客户资料模板字段
        for (String key : headers) {
            head.add(Arrays.asList(key));
        }

        return head;
    }

    @Override
    public List<String> parseData(Map<String, String> data) {
        data = PhoneCryptor.getInstance().decryptMap(data,new String[]{"TEL_NUM1"},false);
        List<String> list = new ArrayList<>();
        list.add(changeName(data.get("CUST_NAME")));
        list.add(changeNum(data.get("TEL_NUM1")));
        list.add(data.get("TASK_NAME"));

        for (String key : columns) {
            list.add(data.get(key));
        }

        return list;
    }

    @Override
    public EasySQL getSql() {
        String objField =  TempFiledUtils.fileds(",t1.", userPrincipal.getSchemaName());
        objField = objField.replace(",t1.CUST_NAME","");
        objField = objField.replace(",t1.TEL_NUM1","");

        EasySQL sql = new EasySQL("select");
        sql.append("t1.OBJ_ID,t1.TEL_NUM1,t1.CUST_NAME,t1.TASK_ID,t2.TASK_NAME");
        sql.append("," +objField);
        sql.append(" FROM "+getTableName("CC_TASK_OBJ t1"));
        sql.append(" LEFT JOIN ").append(getTableName("CC_TASK t2")).append(" on t1.TASK_ID = t2.TASK_ID ");
        sql.append(" WHERE 1=1  and t1.TASK_STATE=0");
        sql.append(params.getString("taskId"), " and t1.TASK_ID = ?");
        sql.append(userPrincipal.getEntId()," and t1.ENT_ID = ? ");
        sql.append(params.getString("batchId"), " and t1.BATCH_ID = ?");
        sql.append(PhoneCryptor.getInstance().decrypt(params.getString("custPhone")), " and t1.TEL_NUM1 = ?");
        sql.appendLike(params.getString("custName"),"and t1.CUST_NAME like ?");
        sql.appendSort(params.getString("sortName"),params.getString("sortType"));
        return sql;
    }

    private void getColumns() {
        try {
            Map<String, String> tempMap = this.getQuery().queryForRow("select " + TempFiledUtils.fileds(",", userPrincipal.getSchemaName()) + " from " + getTableName("cc_cust_temp") + " where TEMP_ID = ?", new Object[]{params.getString("tempId")}, new MapRowMapperImpl());
            for (String key : tempMap.keySet()) {
                String jsonStr = tempMap.get(key);
                if (StringUtils.notBlank(jsonStr)) {
                    JSONObject jsonObject = JSONObject.parseObject(jsonStr);
                    String inuse = jsonObject.getString("inuse");
                    if ("1".equals(inuse)) {
                        String dataType = jsonObject.getString("dataType");
                        //排除客户姓名和客户号码的字段
                        if (!key.startsWith("CUST_NAME") && !key.startsWith("TEL_NUM")){
                            headers.add(jsonObject.getString("name"));
                            columns.add(key);
                        }

                        dataTypeMap.put(key, dataType);
                        if (key.startsWith("TEL_NUM") && "phone".equals(dataType)){
                            num = true;
                        }

                    }
                }
            }
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
        }
    }


    private String changeNum(String phone) {
        if (num){
            return advReplace(phone,3,5,"*");
        }
        return phone;
    }

    private String changeName(String custName) {
        if (StringUtils.isBlank(custName)){
            return null;
        }
        String dataType = dataTypeMap.get("CUST_NAME");
        if (StringUtils.isBlank(dataType)){
            return custName;
        }
        if ("userName".equals(dataType)){
            return advReplace(custName,1,3,"*");
        }
        if ("hide".equals(dataType)){
            return "--";
        }
        return custName;
    }

    private String advReplace(String text, int start, int length, String placeText) {
        if (text == null) return "";
        placeText = placeText != null ? placeText : "*";

        if (Math.abs(start) > text.length()) return text;

        if (start > 0) {
            String textArr1 = text.substring(0, start);
            int end = start + length;
            boolean longText = end > text.length();
            String textArr2 = longText ? "" : text.substring(end);
            int replaceArrLength = longText ? text.length() - start : length;
            return textArr1 + repeat(placeText, replaceArrLength) + textArr2;
        } else {
            String end = text.substring(text.length() + start);
            int startlen = text.length() + start - length;
            int replacelen = startlen > 0 ? length : length + startlen;
            int startIndex = Math.max(startlen, 0);
            String startText = text.substring(0, startIndex);
            return startText + repeat(placeText, replacelen) + end;
        }
    }

    private String repeat(String str, int times) {
        return new String(new char[times]).replace("\0", str);
    }
}
