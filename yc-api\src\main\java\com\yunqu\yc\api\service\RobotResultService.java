package com.yunqu.yc.api.service;

import java.util.HashSet;
import java.util.Set;

import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.api.base.QueryFactory;
import com.yunqu.yc.api.context.EntContext;
import com.yunqu.yc.api.context.TaskContext;
import com.yunqu.yc.api.executor.EventDispatcher;
import com.yunqu.yc.api.executor.impl.NotifyExecutor;
import com.yunqu.yc.api.log.RobotLogger;
import com.yunqu.yc.api.log.TaskLogger;
import com.yunqu.yc.api.robot.RobotTask;
import com.yunqu.yc.api.util.HCodeUtil;
import com.yunqu.yc.api.util.TaskObjTableUtils;


/**
 * 处理机器人外呼结果
 * 
{
	"clientId": "ivr2@172.16.86.15:7553",
	"userData": {},
	"messageType": "cmdService",
	"data": {
		"serviceId": "ROBOT_RESULT_SERVICE",
		"value": {
			"result": "C",
			"score": "40",
			"userData": "{\"batchId\":\"83593006228625270065177\",\"busiOrderId\":\"83791170232668295846336\",\"callSerialId\":\"83593006228355269930035\",\"callbackUrl\":\"http://************:9059/yc-api/callback\",\"called\":\"013469972713\",\"caller\":\"32255688\",\"curCallIndex\":\"1\",\"custInfo\":\"{\\\"客户电话1\\\":\\\"13469972713\\\",\\\"batchId\\\":\\\"83593006456112101013296\\\"}\",\"custPhone\":\"13469972713\",\"entId\":\"1011\",\"flowName\":\"aidept\",\"ivrBeginTime\":\"2021-12-28 21:51:34\",\"maxCallIndex\":\"1\",\"objId\":\"83593006473902101291521\",\"ocsSkillGroup\":\"\",\"queueFile\":\"\",\"realCalled\":\"13469972713\",\"robotId\":\"mk_824\",\"taskId\":\"83605349113425508631232\",\"welcomeFile\":\"\",\"ivrEndTime\":\"2021-12-28 21:52:23\",\"recordFileName\":\"1011/airec/20211228/32255688_013469972713_302252211_215134\",\"recordId\":\"20211228215223_32255688_20211228215134302252211\"}",
			"agentRelease": "4",
			"params": {
				"confirm": "1",
				"specificAddress": "武汉市汉阳区四新南路",
				"reason": "0",
				"isContact": "0"
			},
			"labels": "[\"开头语\",\"1.核实地址（有卡顿）\",\"需要检测\",\"4.结束（不需要）\"]"
		}
	},
	"entId": "1011",
	"sessionId": "1200A841611E5C950A137AA661CB1697",
	"seq": "302252211"
}

userData:

{
	"batchId": "83593006228625270065177",
	"busiOrderId": "83791170232668295846336",
	"callSerialId": "83593006228355269930035",
	"callbackUrl": "http://************:9059/yc-api/callback",
	"called": "013469972713",
	"caller": "32255688",
	"curCallIndex": "1",
	"custInfo": "{\"客户电话1\":\"13469972713\",\"batchId\":\"83593006456112101013296\"}",
	"custPhone": "13469972713",
	"entId": "1011",
	"flowName": "aidept",
	"ivrBeginTime": "2021-12-28 21:51:34",
	"maxCallIndex": "1",
	"objId": "83593006473902101291521",
	"ocsSkillGroup": "",
	"queueFile": "",
	"realCalled": "13469972713",
	"robotId": "mk_824",
	"taskId": "83605349113425508631232",
	"welcomeFile": "",
	"ivrEndTime": "2021-12-28 21:52:23",
	"recordFileName": "1011/airec/20211228/32255688_013469972713_302252211_215134",
	"recordId": "20211228215223_32255688_20211228215134302252211"
}

 * <AUTHOR>
 *
 */

public class RobotResultService extends IService {
	
	private TaskContext taskContext = null;
	private EasyQuery easyQuery = null;
	private EntContext context = null;

	/**
	 * @param jsonObject JSONObject {"taskId":"任务ID,"status:1,2,3"}  
	 * @return JSONObject 
	 */
	@Override
	public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
		
		
		RobotLogger.getLogger().info("[Result] << "+jsonObject);
		TaskLogger.getLogger().info("[Result] << "+jsonObject);
		
		JSONObject dataObject = jsonObject.getJSONObject("data");
		String entId = jsonObject.getString("entId");
		
		JSONObject resultObject =  new JSONObject();
		
		if(StringUtils.isBlank(entId)){
			resultObject.put("desc", "entId is null!");
			resultObject.put("result", "500");
			RobotLogger.getLogger().error(resultObject);
			TaskLogger.getLogger().error(resultObject);
			return resultObject;
		}
		
		
		JSONObject valueObject = dataObject.getJSONObject("value");
		//(必填)营销结果： A B C D E F
		String result = valueObject.getString("result");
		
		//(必填)分数，0~100分
		String score = valueObject.getString("score");
		
		String agentRelease = valueObject.getString("agentRelease");
		String clearCause = valueObject.getString("clearCause");
		if(StringUtils.isBlank(clearCause)){
			clearCause = "0";
		}
		
		if(StringUtils.isBlank(agentRelease)){
			agentRelease = "1";
		}
		

		//营销结果标签，JSON数组
		JSONArray labels = valueObject.getJSONArray("labels");
		String labelString = "";
		if(labels != null){
			/*
			 * fix:cly:20240523 去除标签冗余字段，数据库长度255
			 */
			JSONArray labelArray = new JSONArray();
			HashSet<String> labelSet = new HashSet<>();
			for(int i=0;i<labels.size();i++){
				String label = labels.getString(i);
				if(labelSet.add(label)){
					labelArray.add(label);
				}
			}
			labelString = labelArray.toJSONString();
		}
		
		JSONObject params = null;
		try {
			params = valueObject.getJSONObject("params");
		} catch (Exception ex) {
			TaskLogger.getLogger().error(ex,ex);
			RobotLogger.getLogger().error(ex,ex);
		}
		if(params == null) params = new JSONObject();
		//录音文件路径
		JSONObject userData = valueObject.getJSONObject("userData");
		
		
		
		if(userData == null){
			resultObject.put("desc", "userData is null!");
			resultObject.put("result", "500");
			TaskLogger.getLogger().error(resultObject);
			RobotLogger.getLogger().error(resultObject);
			return resultObject;
		}
		
		if(StringUtils.isBlank(result)){
			resultObject.put("desc", "param[result] is null!");
			resultObject.put("result", "500");
			TaskLogger.getLogger().error(resultObject);
			RobotLogger.getLogger().error(resultObject);
			return resultObject;
		}
		
		if(StringUtils.isBlank(score)){
			resultObject.put("desc", "param[score] is null!");
			resultObject.put("result", "500");
			TaskLogger.getLogger().error(resultObject);
			RobotLogger.getLogger().error(resultObject);
			return resultObject;
		}
		
		EasyCalendar calendar 	= EasyCalendar.newInstance();
		//根据ICCS的信息生成坐席事件模型
		easyQuery = QueryFactory.getRobotQuery(entId);
		context = EntContext.getContext(entId);
		String busiOrderId = userData.getString("busiOrderId");
		
		//任务ID
		String taskId = userData.getString("taskId");
		String batchId = userData.getString("batchId");
		
		//fix cly:减少查询数据库次数
		TaskContext task = TaskContext.getContext(taskId, entId);
		if(task == null){
			resultObject.put("desc", "task[taskId:"+taskId+"] not found!");
			resultObject.put("result", "500");
			TaskLogger.getLogger().error(resultObject);
			RobotLogger.getLogger().error(resultObject);
			return resultObject;
		}
		
		//任务结束，删除当前IVRSession的监控信息。
		RobotTask robotTask = RobotTask.getRobotTask(entId, taskId);
		//String seq = jsonObject.getString("seq");
		
		JSONObject extData = valueObject.getJSONObject("extData");
		if(extData == null) extData = new JSONObject();
		
		
		
		//机器人名单ID
		String objId = userData.getString("objId");
		robotTask.removeSession(objId);
		
		EasyRecord record  = null ;
		
		//开始时间
		String ivrBeginTime = StringUtils.trimToEmpty(userData.getString("ivrBeginTime"));
		//结束时间
		String ivrEndTime = StringUtils.trimToEmpty(userData.getString("ivrEndTime"));
		//主叫
		String caller = StringUtils.trimToEmpty(userData.getString("caller"));
		//被叫
		String called = StringUtils.trimToEmpty(userData.getString("called"));
		//录音ID
		String recordId = StringUtils.trimToEmpty(userData.getString("recordId"));
		//录音文件名
		String recordFileName = StringUtils.trimToEmpty(userData.getString("recordFileName"));
		
		String userAlertingTime = StringUtils.trimToEmpty(userData.getString("userAlertingTime"));
		int aleringTime = 0;
		//只有在非接通状态的时候才写，正常状态通过机器人结果或者坐席来写。
		//cly20231208 机器人结果使用robotSerialId作为录音ID，保留callSerialId
		String callSerialId = userData.getString("callSerialId");
		String robotSerialId = userData.getString("robotSerialId");
		if(StringUtils.isBlank(robotSerialId)){
			robotSerialId = callSerialId;
		}
		//ocj，解决老版本sipCallId写不进userData，增加适配-消互项目
		String sipCallId = StringUtils.trimToEmpty(userData.getString("sipCallId"));
		if(StringUtils.isBlank(sipCallId)){
			sipCallId=StringUtils.trimToEmpty(valueObject.getString("sipCallId"));
		}
		
		if(isBlankDateTime(ivrBeginTime) || isBlankDateTime(userAlertingTime)){
				//
		}else{
			try {//总的通话时间=接通进入IVR时间-坐席释放时间
					aleringTime = calendar.diff(userAlertingTime,ivrBeginTime, "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
			} catch (Exception ex) {
			}
		}
		
		String calledAreaCode = StringUtils.trimToEmpty(HCodeUtil.getAracodeByCalled(entId, caller, called));
		
		called = HCodeUtil.formatCalled(entId, caller, called);

		int totalTime = 0;
		
		try {

			
			record = new EasyRecord(context.getTableName("CC_CALL_RECORD"),"SERIAL_ID");
			String createTime = ivrBeginTime;
			if (isBlankDateTime(ivrBeginTime) || isBlankDateTime(ivrEndTime)) {
				//
			} else {
				try {// 总的通话时间=接通进入IVR时间-坐席释放时间
					totalTime = calendar.diff(ivrBeginTime, ivrEndTime,
							"yyyy-MM-dd HH:mm:ss", EasyCalendar.SECOND);
				} catch (Exception ex) {
					// TODO: handle exception
				}
			}
			
			if(totalTime<0){
				ivrEndTime = ivrBeginTime;
				totalTime = 0 ;
			}

			record.setPrimaryValues(robotSerialId);
			record.set("BUSI_CALL_ID", callSerialId);
			record.set("MONTH_ID", calendar.getFullMonth());
			record.set("DATE_ID", calendar.getDateInt());
			record.set("CREATE_TIME", createTime);
			//ivr 获取不到振铃时间
			if(!isBlankDateTime(userAlertingTime)){
				record.set("AGENT_STAY_TIME", aleringTime);  //外呼振铃时间，这里保存的用户的振铃时间
				record.set("AGENT_TIME", userAlertingTime); 
			}
			record.set("BILL_BEGIN_TIME", ivrBeginTime);
			record.set("BILL_END_TIME", ivrEndTime);
			record.set("BILL_TIME", totalTime);
			record.set("FEE_TIME_6", getBillTime(totalTime,6));  //6秒计费时长
			record.set("FEE_TIME_60", getBillTime(totalTime,60));  //分钟计费时长
			record.set("BUSI_ORDER_ID", userData.getString("busiOrderId"));
			record.set("ENT_ID", entId);
			record.set("OBJ_ID", objId); // 任务对象
			record.set("CREATE_CAUSE", 8); // 呼叫创建原因,8 智能外呼
			record.set("TASK_ID", taskId); // 任务ID
			record.set("TASK_NAME", task.getString("TASK_NAME")); // 任务名称
			record.set("CLEAR_CAUSE", clearCause); //挂机原因
			record.set("CALLER", caller); // 主叫
			record.set("CALLED", called); // 被叫
			record.set("CUST_PHONE", called); // 被叫
			record.set("CALL_TIMESTAMP", System.currentTimeMillis()/1000);
			record.set("RES_TYPE", 1);
			if(StringUtils.isNotBlank(recordId)){
				record.set("RECORD_ID",recordId);
			}
			if(StringUtils.isNotBlank(recordFileName)){
				record.set("RECORD_FILE", recordFileName);
			}
			if(StringUtils.isNotBlank(agentRelease)){
				record.set("AGENT_RELEASE", agentRelease);
			}
			if(StringUtils.isNotBlank(sipCallId)){
				record.set("SIP_CALL_ID", sipCallId);
			}
			
			record.set("ROBOT_ID", task.getString("IVR_FLOW_NAME"));
			
			record.set("AREA_CODE",calledAreaCode);
			
			if(extData.containsKey("data1")) record.set("DATA1", extData.getString("data1"));
			if(extData.containsKey("data2")) record.set("DATA2", extData.getString("data2"));
			if(extData.containsKey("data3")) record.set("DATA3", extData.getString("data3"));
			if(extData.containsKey("data4")) record.set("DATA4", extData.getString("data4"));
			if(extData.containsKey("data5")) record.set("DATA5", extData.getString("data5"));
			
			boolean bl = easyQuery.update(record);
			try {		
				if(!bl) easyQuery.save(record);
			} catch (Exception ex) {
				RobotLogger.getLogger().error("cc_call_record insert  error,cause:"+ex.getMessage());
				TaskLogger.getLogger().error("cc_call_record insert  error,cause:"+ex.getMessage());
				easyQuery.update(record);
			}
		} catch (Exception ex1) {
			RobotLogger.getLogger().error("Save robot cc_call_record error,cause:"+ex1.getMessage(),ex1);
			TaskLogger.getLogger().error("Save robot cc_call_record error,cause:"+ex1.getMessage(),ex1);
		}
		
		
		String sql = "";
		try {
			sql = "update " +TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ") + " set TASK_STATE = 9 , CALL_TIMES = CALL_TIMES+1, SUCC_CALL_TIMES = SUCC_CALL_TIMES+1 , CALL_RESULT = ?  , SALE_FINISH_TIME = ?,RUN_DATE = ?,TOTAL_TIME=TOTAL_TIME+"+totalTime+" where OBJ_ID = ?  ";
			easyQuery.executeUpdate(sql, new Object[]{0,calendar.getDateTime("-"),calendar.getDateInt(),objId});
		} catch (Exception ex) {
			RobotLogger.getLogger().error(ex,ex);
			TaskLogger.getLogger().error(ex,ex);
		}
		
		sql = "select CUST_ID,CUST_NAME,BATCH_ID from "+TaskObjTableUtils.getObjTn(taskId, entId, "CC_TASK_OBJ")+" where OBJ_ID = ?";
		
		RobotLogger.getLogger().info("OBJ_ID="+objId+",TASK_ID="+taskId+",PxURPOSE="+result+",SCORE="+score+",LABELS="+labels);
		try {
			
			EasyRow row = easyQuery.queryForRow(sql, new Object[]{objId});
			String custId   = "";
			String custName = "";
			if(row!=null){
				custId = row.getColumnValue("CUST_ID");
				custName = row.getColumnValue("CUST_NAME");
				batchId = row.getColumnValue("BATCH_ID");
			}
			
			//保存机器人执行结果
			//cly20231208 支持根据任务分表
			record = new EasyRecord(TaskObjTableUtils.getObjRobotTableName(taskId,context.getSchemaId()),"OBJ_ID");
			record.setPrimaryValues(objId);
			record.set("ENT_ID", entId);
			record.set("MONTH_ID", calendar.getFullMonth());
			record.set("DATE_ID", calendar.getDateInt());
			record.set("TASK_ID", taskId);  //任务ID
			record.set("BATCH_ID", batchId);  //任务ID
			record.set("CUST_ID", custId);  //客户编号
			record.set("SERIAL_ID",  robotSerialId);  //录音记录ID
			record.set("CUST_NAME",  custName);  //客户编号
			record.set("CUST_PHONE", called);  //客户电话
			record.set("STD_CLASS", result);  //标准分类
			record.set("STD_CLASS_CAUSE", "");  //标准分类原因
			record.set("SCORE", Integer.parseInt(score));
			record.set("LABELS", labelString);
			record.set("BEGIN_TIME", ivrBeginTime);  //外呼开始时间
			record.set("END_TIME", ivrEndTime);  //外呼结束时间
			record.set("CALL_TIME", totalTime);  //通话时长
			record.set("CLEAR_CAUSE", 0);  //挂机原因
			record.set("READ_FLAG", 0);  //已读状态，0 未读  1 已读
			record.set("AGENT_FLAG", 0);  //转坐席标志，0 未转坐席  1 转坐席
			record.set("RECORD_FILE", recordFileName);
			if(params!=null){
				/*
				 * fix:cly:20240523 字段太长，数据库字段脚本长度才500，入库要做限制
				 * 参数机器那边应用比较广泛，如果太长了，这个字段不跟新
				 */
				String paramsStr = params.toJSONString();
				if(params.size()<=170){
					record.set("PARAMS", paramsStr);
				}else{
					RobotLogger.getLoggerParam().error("【提参过长】"+jsonObject);
				}
			}

			//fix:20240119 cly 添加意向等字段字段
			record.set( "FINISH_FLAG", valueObject.getIntValue("isFinish"));
			record.set( "INTENTTION_FLAG", valueObject.getIntValue("isIntenttion"));
			record.set( "BLACKLIST_FLAG", valueObject.getIntValue("isBlackList"));
			record.set( "ARTIFICIAL_FLAG", valueObject.getIntValue("isArtificial"));
			record.set( "SATISFIED_FLAG", valueObject.getIntValue("isSatisfied"));
			record.set( "FIRSTINTENT_FLAG", valueObject.getIntValue("isFirstintent"));
			record.set( "COMPLAINTS_FLAG", valueObject.getIntValue("isComplaints"));
			record.set( "SEND_SMS_FLAG", valueObject.getIntValue("isSendSms"));
			record.set( "CAHT_ROTATION", valueObject.getIntValue("chatRotation"));
			record.set( "VALID_CAHT_ROTATION", valueObject.getIntValue("validchatRotation"));

			EntContext.getContext(entId).checkRecord(record);
			
			this.evedayTask(taskId, entId, robotSerialId, record);	
			boolean bl = easyQuery.update(record);
			if(!bl){
				easyQuery.save(record);
			}
		
		} catch (Exception ex) {
			RobotLogger.getLogger().error(ex,ex);
			TaskLogger.getLogger().error(ex,ex);
			resultObject.put("desc", ex.getMessage());
			resultObject.put("result", "500");
		}
		
		try {  
			//保存提参信息。
			String busiId = context.getBusiId(busiOrderId);
			if("201".equals(busiId)){
				//智能外呼
				this.saveRobotParamYcCall(taskId, objId, params);
			}else{
				this.saveRobotParam(taskId, objId, params);
			}
		} catch (Exception ex) {
			TaskLogger.getLogger().error(ex,ex);
			RobotLogger.getLogger().error(ex,ex);
		}
		
		try {
			JSONObject smsParam = new JSONObject();
			JSONObject smsDataObject = new JSONObject();
			JSONObject smsValueObject = new JSONObject();
			smsValueObject.put("event", "evtDisConnected");
			smsValueObject.put("userData", userData);
			smsDataObject.put("value", smsValueObject);
			smsParam.put("data", smsDataObject);
			IVRTaskSmsService smsService = new IVRTaskSmsService();
			smsService.invoke(smsParam);
		} catch (Exception ex) {
			TaskLogger.getLogger().error("发送挂机短信失败，原因："+ex.getMessage(),ex);
		}

		this.taskResultNotify(robotSerialId,valueObject);
		
		resultObject.put("desc", "succ");
		resultObject.put("result", "0");
		return resultObject;
	}
	
	
	/**
	 * 保存机器人提参信息
	 * @param taskId
	 * @param objId
	 * @param paramObj
	 */
	private void  saveRobotParam(String taskId,String objId,JSONObject paramObj) throws Exception{
		String sql = "select * from "+ context.getTableName("CC_TASK_ROBOT_PARAM")+" where TASK_ID = ? ";
		JSONObject params  =  easyQuery.queryForRow(sql, new Object[]{taskId},new JSONMapperImpl());
		//如果没有找到提参记录，则创建一条针对任务的提参定义记录。
		if(params == null){
			sql = "insert into "+ context.getTableName("CC_TASK_ROBOT_PARAM") +"(TASK_ID,DATE_ID) values(?,?)";
			easyQuery.executeUpdate(sql, new Object[]{taskId,EasyCalendar.newInstance().getDateInt()});
			params = new JSONObject();
		}
		//cly20231208 支持按任务分表
		String paramTableName = TaskObjTableUtils.getObjRobotParamTableName(taskId,context.getSchemaId());
		EasyRecord record = new EasyRecord(paramTableName, "OBJ_ID");
		record.setPrimaryValues(objId);
		record.set("TASK_ID", taskId);
		record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
		Set<String> keys = paramObj.keySet();
		for(String key :keys){
			int idx = this.getParamIndex(params, key);
			if(idx == 0) continue;
			String paramKey;
			if(idx>100){  //大于100代表这个key是新的提参，需要创建该提参的信息。
				paramKey = "PARAM"+(idx-100);
				JSONObject  keyObj = new JSONObject();
				keyObj.put("value", key);
				keyObj.put("text", key);
				keyObj.put("options", new JSONArray());
				params.put(paramKey, keyObj);
				sql = "update "+ context.getTableName("CC_TASK_ROBOT_PARAM")+" set "+paramKey+"=? where TASK_ID = ? ";
				easyQuery.executeUpdate(sql, new Object[]{keyObj.toJSONString(),taskId});
			}else{
				paramKey = "PARAM"+idx;
			}
			record.set(paramKey, paramObj.getString(key));
		}
		boolean bl = easyQuery.update(record);
		if(!bl) easyQuery.save(record);
	}

	/**
	 * 更新
	 * @param taskId
	 * @param objId
	 * @param paramObj
	 * @throws Exception
	 */
	private void saveRobotParamYcCall(String taskId,String objId,JSONObject paramObj) throws Exception{
		JSONObject robotParamKey = this.getRobotParamKey(taskId);
		//有配提参和同步后才执行
		if(robotParamKey.size()>0){
			//cly20231208 支持按任务分表
			EasyRecord record = new EasyRecord(TaskObjTableUtils.getObjRobotParamTableName(taskId,context.getSchemaId()), "OBJ_ID");
			record.setPrimaryValues(objId);
			record.set("TASK_ID", taskId);
			record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
			Set<String> keys = paramObj.keySet();
			for(String key :keys){
				String paramKey = robotParamKey.getString(key);
				if(StringUtils.isNotBlank(paramKey)){
					record.set(paramKey, paramObj.getString(key));
				}
			}
			boolean bl = easyQuery.update(record);
			if(!bl) easyQuery.save(record);
		}
	}

	/**
	 * 查询机器人提参配置
	 * @param taskId
	 * @return
	 */
	private JSONObject getRobotParamKey(String taskId){
		JSONObject paramKey = new JSONObject();
		try {
			String sql = "select * from "+ context.getTableName("CC_TASK_ROBOT_PARAM")+" where TASK_ID = ? ";
			JSONObject params  =  easyQuery.queryForRow(sql, new Object[]{taskId},new JSONMapperImpl());
			if(params != null){
				for (String key : params.keySet()) {
					String val = params.getString(key);
					if(key.indexOf("PARAM")<0){
						continue;
					}
					if(StringUtils.isBlank(val) || !val.startsWith("{")){
						continue;
					}
					JSONObject param = JSONObject.parseObject(val);
					val = param.getString("value");
					if(StringUtils.isBlank(val)){
						continue;
					}
					paramKey.put(val, key);
				}
			}
		} catch (Exception e) {
			RobotLogger.getLogger().error(e.getMessage(), e);
		}
		return paramKey;
	}
	
	private int getParamIndex(JSONObject params,String key){
		for(int i = 1 ;i<=20 ;i++){
			String idx = "PARAM"+i;
			String jsonString = params.getString(idx);
			if(StringUtils.isBlank(jsonString)) return  100+i;  //首先判断当前参数是否已经被定义，如果没有则获取一个下标来保存这个参数。
			JSONObject paramObj = JSONObject.parseObject(jsonString);
			String value = paramObj.getString("value");
			if(value.equalsIgnoreCase(key)) return i;
		}
		return 0 ;
	}

	/**
	 * 使用多线程调度进行通知
	 * @param serialId
	 * @param taskResult
	 */
	public void taskResultNotify(String serialId,JSONObject taskResult){
		EventDispatcher.addEvent(new NotifyExecutor("ROBOT-TASK-RESULT-", serialId,taskResult));
	}
	
	/**
	 * 每天重呼数据，另外写入历史表
	 * @param taskId
	 * @param entId
	 * @param serialId
	 * @param record
	 */
	private void evedayTask(String taskId,String entId,String serialId, EasyRecord record){

		//呼叫历史
		taskContext = TaskContext.getContext(taskId, entId);
		if(taskContext.isEvedayCall()){//每天重呼数据
			try {
				//cly20231208 支持按任务分表
				if(StringUtils.isNotBlank(record.getString("STD_CLASS")))
					easyQuery.execute("delete from "+TaskObjTableUtils.getObjRobotHisTableName(taskId,context.getSchemaId())+" where OBJ_ID = ? and DATE_ID = ?", new Object[]{record.getString("OBJ_ID"),record.getString("DATE_ID")});
				
				EasyRecord easyRecord = new EasyRecord(TaskObjTableUtils.getObjRobotHisTableName(taskId,context.getSchemaId()),"CALL_OBJ_ID");
				easyRecord.setColumns(record);
				easyRecord.setPrimaryValues(serialId);
				easyQuery.save(easyRecord);
			} catch (Exception ex) {
				TaskLogger.getLogger().error(ex.getMessage(),ex);
				RobotLogger.getLogger().error(ex.getMessage(),ex);
			}
		}
	}
	
	
	private static int  getBillTime(int callTime,int unit){
		return  callTime/unit + ((callTime%unit>0)?1:0);
	}
	
	private boolean isBlankDateTime(String dateTime){
		if(StringUtils.isBlank(dateTime)) return true;
		if(dateTime.startsWith("1970")) return true;
		return false;
	}
	
}