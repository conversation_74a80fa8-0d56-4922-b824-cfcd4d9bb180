package com.yunqu.yc.portal.servlet.export;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.portal.base.AppBaseExportServlet;
import com.yunqu.yc.portal.excel.EasyExcelUtil;
import com.yunqu.yc.portal.excel.export.TaskRobotNoResultExportHandler;
import com.yunqu.yc.portal.excel.export.TaskRobotResultExportHandler;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;

@WebServlet("/servlet/task/export")
public class TaskExportServlet extends AppBaseExportServlet {

    /**
     * 机器人已外呼数据导出
     */
    public void actionForExportRobotResult(){
        try {
            JSONObject params = getParameters();
            if (StringUtils.isBlank(params.getString("taskId"))){
                getResponse().getWriter().write("taskId不能为空");
                return;
            }
            TaskRobotResultExportHandler handler = new TaskRobotResultExportHandler(getUserPrincipal(),params);
            EasyExcelUtil.export(getResponse().getOutputStream(),handler);
        } catch (Exception e) {
            renderText("导出失败");
        }
    }

    /**
     * 机器人未外呼数据导出
     */
    public void actionForExportRobotNoResult(){
        try {
            JSONObject params = getParameters();
            if (StringUtils.isBlank(params.getString("taskId"))){
                getResponse().getWriter().write("taskId不能为空");
                return;
            }
            TaskRobotNoResultExportHandler handler = new TaskRobotNoResultExportHandler(getUserPrincipal(),params);
            EasyExcelUtil.export(getResponse().getOutputStream(),handler);
        } catch (Exception e) {
            renderText("导出失败");
        }
    }

}
