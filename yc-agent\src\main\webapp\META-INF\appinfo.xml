<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="yc-agent" name="云客服标准版" package-by="Vorte" package-time="2025-07-17 15:37:35" resTable="ycBusiRes#003" version="3.4.1#20250717-4">
    <datasources>
        <datasource description="云客服数据源(写1)" isnull="true" name="yc-wirte-ds-1"/> 
        <datasource description="云客服数据源(写2)" isnull="true" name="yc-wirte-ds-2"/> 
        <datasource description="云客服数据源(读)" isnull="true" name="yc-read-ds"/> 
    </datasources>
    <description>
		3.5.0#20250609-1
			1、打版
		3.4.1#20250317-1
			1、修复漏话导出技能组缺失的问题。
		3.4.1#20250113-1
			1、添加视频外呼；
		3.4.1#20241216-1
			1、话单新增查询兼容历史表；
    	3.4.1#20241216-1
    		1、添加应用配置【INVALID_BUSI_WARN_TIME】，设置过期预警；
    	3.4.1#20241209-1
    		1、添加全局应用配置【CLOSE_DATA_CHECK】&gt;"Y"，如果配置上了，所有的数据查询，不再验证角色的数据权限，默认不配置
    	3.4.1#20241204-1
    		1、添加企业拓展配置【CLOSE_DATA_CHECK】&gt;"Y"，如果配置上了，所有的数据查询，不再验证角色的数据权限，默认不配置；
    	3.4.1#20241118-1
    		1、处理工单导出和客户资料导出；
		3.4.1#20241115-1
			1、新增外部js引入配置
     	3.4.1#20241114-1
     		1、添加信息完整性校验功能，页面添加按钮BTN_DATA_VERIFY，需要在菜单配置加上才能生效，主要是针对密评；2、添加视频播放
    	3.4.1#20241112-1
    		1、调整客户模板；2、调整号码加密逻辑，拓展外部加密规则PHONE_CRYPTOR_SERVICE；3、调整号码查询改变in的方式
		3.4.1#20241023-1
			1、通话录音导出支持批量导出，优化导出性能
    	3.4.1#20241011-1
    		1、改动windowOpen方法，实现下载进度条，下载进度是虚拟的，主要作用是防止多次下载点击；
    	3.4.1#20241010-1
    		1、调整通话记录导出支持easyexcel
    	3.4.1#20240920-1
    		1.在企业拓展配置中加“CHECK_USER_AUTH”中配置“N”后不校验权限控制；
		3.4.1#20240919-1
			1.坐席状态时市场统计报表优化
		3.4.1#20240918-1
			1.优化通话记录相关页面，查询和导出的最大间隔天数校验。
			2.增加动态版本号“appVersion”，用于静态资源加载。
    	3.4.1#20240913-1
    		1、改变通话记录查询界面风格；
    	3.4.1#20240909-1
    		1、添加订购将要过期提醒功能；
		3.4.1#20240813-1
			1、优化通话记录导出呼叫结果字典
    	3.4.1#20240715-1
    		1、限制按钮秒点多次情况发生，主要是提交的时候，防止提交多次；2、调整留言界面；3、放开转满意度按钮；
    	3.4.1#20240705-1
    		1、调整报表权限控制
    	3.4.1#20240527-1
    		1、支持企业拓展配置添加PHONE_CRYPT_EXPOR=true时，支持导出加密另外控制；
		3.4.1#202400527-1
			1、修改后台方法  修改WholeOperateDao.list方法查询日期仅能查询到当日之前的数据
		    2、修改WholeOperateSql.wholeOperateSql2 按照DATE_ID倒叙排序
			3、修改后台方法  ServiceLevelDao.serviceLevelList2方法查询日期仅能查询到当日之前的数据
			4、修改ServiceLevelSql.wholeOperateSql2 按照DATE_ID倒叙排序
    	3.4.1#202400425-1
    		1、添加坐席助手功能；
		3.4.1#202400417-1
			1.增加逻辑：首页加载时自动完成单点登录到其他系统。单点登录配置模板：yc-agent\WebContent\META-INF\ssoLoginTemp.txt
		3.4.1#202400410-1
			1.优化国际化工具类I18nUtil.java 无法读取到语言源文件的问题。
			2.注意：编译代码时，必须将i18n目录添加到resource。
    	3.4.1#20240226-1
    		1、处理我的通话记录回拨不了问题；
    	3.4.1#20240202-1
    		1、漏话列表添加【agentRelease】
		3.4.0#20240124-1
			1、兼容工单号码开头为0时的外地号码无法查询联系历史的情况
			2、修复工单重复提交的问题
    	3.4.0#20240116-1
    		1、异机登陆时，自动退出添加配置控制；
    		2、处理工作日志长度限制问题；
		3.4.0#20231212-1
			1.优化文件上传，增加校验文件类型是否在白名单中。
			2.将部分资源加入到登录验证中，删除http-method配置（PUT，DELETE，HEAD，OPTIONS，TRACE）。
    	3.4.0#20231201-1
    		1、异机登陆时，自动退出；
    		2、30分钟未操作时，自动退出；
    		3、需要在企业拓展配置上，添加参数timeoutLogout=Y
    	3.4.0#20231130-1
    		1、页面加载号码根据配置隐藏真实号码；
    	3.4.0#20231129-1
    		1、添加异机登陆时退出；2、添加30分钟不操作，自动退出；
    	3.4.0#20231128-1
    		1、通话记录挂机环节补全；
    	3.4.0#20231110-1
    		1、根据新疆要求，坐席一个坐席统计
		3.4.0#20231022-1
			1、增加支持上下文跳转。
    	3.4.0#20231011-1
    		1、处理报表权限控制问题；
		3.4.0#20230920-1
			1、工单保存新增前端传入SERIAL_ID
    	3.4.0#20230918-1
    		1、修改漏话统计，根据漏话原因计算统计；
    		2、漏话查询列表一般不显示排队时间、振铃时间，如果需要显示可以在菜单后面加上参数【misType=all】
    	3.4.0#20230905-1
    		1、新增外呼客户号码拨号盘菜单（手动添加）（agent-call-telnum）（/yc-agent/pages/entmgr/call-telnum.jsp）
    	3.4.0#20230619-1
    		1、添加如果网络断开，就一直报警
    	3.4.0#20230612-1
    		1、封版；
		3.3.0#20230724-1
			1、优化导出功能文字提示
		3.3.0#20230605-1
			1、每日运营情况统计报表sql优化。
			2、修改工单详情页面工单流水号显示。
		3.3.0#20230531-1
			1、新增报表数据权限
		3.3.0#20230522-1
			1、通话录音查询新增工单跳转
		3.3.0#20230518-1
			1、修改工单导出文件名命名规则
		3.3.0#20230515-1
			1、工单流水号修改为自增，并优化工单列表页面展示。
			2、修复工单客户资料查询问题。
			3、修复工单客户资料再次来电客户资料不能修改的问题。
			4、修复工单导出客户资料为空的问题。
		3.3.0#20230511-1
			1、工单新增流水号日自增功能
		3.3.0#20230508-1
			1、修复模板编辑字段编码不显示的问题
			2、修复工单缺失CUST_ID的问题
		3.3.0#20230505-1
			1、工单导出配置导出字段优化、导出字段BUG修复、
		3.3.0#20230504-1
			1、我的工单新增导出功能；
			2、我的工单隐藏客户信息，默认显示客户详情。
		3.3.0#20230504-1
			1、工单新增导出功能；
    	3.3.0#20230421-1
    		1、修改工单执行界面；
    	3.3.0#20230412-1
    		1、漏话查询添加坐席信息；
    	3.3.0#20230411-1
    		1、修改样式问题；2、修改漏话查询问题；
		3.3.0#20230404-1
			1、工单新增附件上传
		3.3.0#20230323-1
			1、优化工单查询，必须带开始日期，默认查询近7天数据
    	3.3.0#20230321-1
    		1、调整工单常用语；2、调整工单客户资料；
		3.3.0#20230320-1
			1、移除工单查询页面的input输入框输入缓存。
			2、优化来电弹屏工单页面token校验。
			3、优化页面菜单权限拦截。
    	3.3.0#20230216-1
    		1、话单查询添加挂机类型；2、话务统计修改bug;
		3.3.0#20230201-1
			1、坐席服务水平报表、整体话务分析报表 修改统计sql为 UNION方式。
			2、坐席服务水平报表、整体话务分析报表 新增月度统计维度
		3.3.0#20230201-1
			1、解决工单排行榜，审批人管理页面500错误。
    	3.3.0#20230130-1
    		1、修改15秒统计不匹配问题；
    		2、添加页面拦截条件；
			3、优化防范CSRF攻击过滤器，解决一个用户同时在不同浏览器登录时会出现token校验失败的问题。
		3.3.0#20221229-1
			1、新增级联配置导入功能
		3.3.0#20221228-1
			1、新增v2工单页面刷新后自动暂存
		3.3.0#20221227-1
			1、修复工单单选、多选必填不生效的问题
    	3.3.0#20221025-1
    		1、修改每日运营统计呼出平均通话时长计算错误问题；
		3.3.0#20220929-1
			1、修改工单侧边栏sql注入的问题
			2、工单页面侧边栏手机号信息兼容优化
		3.3.0#20220927-1
			1、修改工单侧边栏显示为根据企业ID显示
    	3.3.0#20220926-1
    		1、修改通话记录问题；
    		2、修改工单侧边栏问题；
    	3.3.0#20220924-1
    		1、修复分时统计问题；
    		2、兼容云客服挂载云电销，支持自动任务来电弹屏；
    		3、解决工作组坐席绑定不能清空问题；
    		4、修改工单回显来电主被；
		3.3.0#20220922-1
			1、新增工单侧边栏配置 2、新增cc_agent_order_tabs工单侧边栏表
	    3.3.0#20220920-1
	     	1、改造修改密码方式；2、优化导出当前页录音方式；
    	3.3.0#20220916-1
    		1、简单工单添加回显主被叫功能；
    		2、简单工单导出功能优化；
    	3.3.0#20220915-1
    		1、修改大屏问题；
    		2、添加工单ivr订购功能；
		3.3.0#20220914-1
			1.全媒体菜单增加“坐席辅助功能权限”菜单。
    	3.3.0#20220913-1
    		1、修改坐席工作日志导出问题；
    	3.3.0#20220907-1
    		1、限制通知长度；
    		2、解决报表查找多坐席问题；
		3.3.0#20220830-1
			1、优化全媒体配置-满意度指标配置
		3.3.0#20220829-1
			1、优化全媒体相关配置
    </description>
	<versionHistory>
		3.3.0#20220822-1
			1、去除所有事务处理；
			2、调整postgreSql语法；
			3、调整快速入口bug;
		3.2.8#20220811-1
			1、优化录音文件读取的逻辑，增加录音文件路径拓展，兼容多台录音文件服务器。
			2、优化全媒体渠道按键配置，增加按键是否显示，需执行脚本 ALTER TABLE ycmain.CC_CHANNEL_KEY ADD IS_SHOW INT DEFAULT 1 COMMENT '按键是否显示，0不显示，1显示';
		3.2.7#20220811-1
			1、删除isRes判断权限；
			2、修改菜单拦截器；
		3.2.6#20220801-1
			1、坐席工作情况添加示忙其他统计，满足接口对接的情况
		3.2.5#20220805-1
			1.首页[话务设置]中新增是否自定义来电振铃音选项，音频文件为localStorage有效；
		3.2.5#20220728-1
			1、新增ivr轨迹查询列表，关联表【CC_IVR_TRACE】；
			2、修复为创建工单模板是，来电弹屏报错问题；
			3、新增坐席状态日志列表，关联表【CC_AGENT_STATE_LOG】；
			4、修改在oracle环境上，工单不能distinct【ORDER_LOG】大字段；
			5、添加企业拓展配置信息到localStorage，使用方式，调用getEntExtConfig(key)；
			6、考虑到很多环境用不到ivr语音播报，默认隐藏，如果需要使用，在企业拓展配置添加【IVR_PALY:1】；
		3.2.4#20220721-1
			1.在坐席工作日志界面新增导出功能【agent-work-log.jsp】
		3.2.3#20220706-1
			1、添加IVR按键配置，添加表【CC_IVR_KEY】，路径/yc-agent/pages/entmgr/ivr-conf-list.jsp；
			2、增加IVR节点统计，路径/yc-agent/pages/report/call/ivrTraceStat.jsp
		3.2.2#20220624-1
			1、修改菜单查询方式；
			2、未配置工单模板时来电弹屏报错；
		3.2.1#20220620-1
			1.当工单模板未配置字段后新建完工单进入已完成工单或我参与工单界面会报错 现已修改
		3.2.1#20220601-1
			1.JSP页面提交增加token校验，用于防止跨站请求伪造（CSRF）攻击.
		3.2.1#20220519-1
			1.优化渠道配置-按键配置，按键可关联队列，不需要在技能组管理中配置服务渠道
		3.2.1#20220519-1
			1、添加菜单拦截器，通过yc-base配置启动，默认不启动；
		3.2.0#20220516-1
			1、通话记录、我的通话记录界面，通话时长查询条件可支持手填查询
			2、解决漏话时间选择bug。
			3、通话记录、我的通话记录界面、漏话 新增可以配置查询天数功能（如该界面地址后面加：?exportMaxDay=120），默认31天
			
		2.8.1#20220424-1
			1、解决通话记录查询页面无法查询到数据，SQL执行超时。
			2、解决通话记录的录音播放不了的问题。
			 1.修改工单排行榜查看了所有企业的数据
			 2.修改工单查询列表中，已完结的工单，“完结时间”显示为空
			 3.存量客户管理页面添加客户资料时 标识“客户编号”必填项（样式）
						 
		2.8.1#20220422-1 1.话务条新增静音功能，得同步升级callserver、iccs
		2.8.1#20220411-1 1.修改工单查询 业务工单→工单管理→已完成的列表中，无“完结时间” 修改文件【OrderDao.java】【OrderServlet.java】
	  	2.8.1#20220407-1 1.修改外线坐席没有振铃弹屏 修改文件【ccbar_agent.js】
	    2.8.1#20220330-1 1.振铃弹屏没有显示出被叫号码问题 修改文件【ccbar_agent.js】
		2.8.1#20220316-2
			1、解决我的通话记录 通话录音bug
    	2、大屏监控数据库权限问题
	    2.8.1#20220316-1 修改工单查询全部界面 根据模板的配置选择导出列表 修改文件【ExportServlet.java】
		2.8.1#20220314-1 1.首页界面的快速入口模块改为可配。添加数据库表【cc_agent_quick_entry】脚本。
						 2.优化工单提醒，添加心跳机制。
		2.8.1#20220303-1 修改外呼弹屏不回显客户资料bug，修改文件【CallUrlService.java】
	    2.8.1#20220228-1 新增振铃弹屏、外呼弹屏功能  需要在9059弹屏配置 进行配置
		2.8.1#20220218-1 修改指派人的bug 修改文件【order-new.jsp】
		2.8.1#20220217-1 关闭新建工单时左上角的框 修改文件【order-new.jsp】
		2.8.1#20220214-1 1.将工单提醒在公告中独立出来 
						 修改文件【NoticeDao.java、PortalCommonDao.java、ReceiveMessageService.java、NoticeAlertServlet.java
							     NoticeServlet.java、notice-list.jsp、portal_common.js、portal_template.jsp、portal-agent.jsp、system.css】
						 添加文件【notice-alert-detail.jsp、notice-alert-list.jsp】
						 添加表  【CC_NOTICE_ALERT】
		2.8.1#20220209-1 1.修改漏话查看规则 管理员可以查看所有漏话 修改文件【CallNoAnswerDao.java】
		2.8.0#20220208-1 1.导出数据量由配置统一控制
		2.7.9#20220128-1 1.优化工单提醒 修改文件OrderServlet.java
		2.7.9#20220127-1 1.优化工单提醒并添加催办类型 修改文件【Constants.java、ReceiveMessageService.java、NoticeAlertServlet.java、NoticeServlet.java
							portal-agent.jsp、portal-engineer.jsp】
		2.7.9#20220124-1 1.通话记录查询适配原来的分页栏 【例：/yc-agent/pages/record/cdr-query.jsp?allExport=true则为显示原来的分页栏】
					     2.修改通话记录查询中客户号码筛选条件不可用bug 修改文件【cdr-query.jsp、my-cdr.jsp】
		2.7.9#20220124-1 1.漏话、未接、留言只留时间统计维度；2.工单级联查询类型问题
		2.7.9#20220121-2 1.修改历史工单一开始加载很多录音文件问题，2修改工单历史录音兼容不了wav格式问题,3导出坐席多选问题
		2.7.9#20220121-2 1.在工单来电弹屏上新增被叫号码修改文件【order-new.jsp】
		2.7.9#20220121-1 1.修改暂存中发起工单数据不回显 修改文件【order-new.jsp】
		2.7.8#20220120-2 1.代办工单查询效率低，添加默认时间，创建索引，提高查询效率
		2.7.8#20220120-1 1.优化工单配置中发送通知和指定人按钮样式 修改文件【template-flow-config.jsp】
						 2.修改bug云客服工单配置好的级联，必选项失效，没选还是能提交 修改文件【form-creater.js】
		2.7.7#20220119-1 优化sql并将RESERVE1字段存储下一级指派人改为HANDLER_ID字段存储并更新HANDLER_TYTPE状态为20 修改文件【order-my-deal.jsp】
						【order-new.jsp】【OrderServlet.java】【OrderDao.java】
		2.7.6#20220117-1 新增指派人功能 在工单模板配置中、每个模板下的每个流程的步骤动作中新增是否开启指派人选项，选择是后此动作中开启指派人，不选默认工单
						 发送全部人，选择人后单独只给这一个人，修改文件【OrderTempDao.java】【OrderTempDao.java】【OrderServlet.java】【CustDataServlet.java】
						 【order-new.jsp】【order-my-deal.jsp】【temp.jsp】【template-flow-config.jsp】【order-core.js】新增在cc_agent_order_action表中新增
						 字段ASSIGN_PERSON功能是'是否开启指派人'，使用cc_agent_order中的RESERVE1保留字段作为存储下一级指派人ID
		2.7.5#20220112-1 新增满意度评价按钮，在后台企业控制，跟据表CC_ENT_VOX字段VOX_TYPE为8并且不存在为4
		2.7.4#20220107-1 修改来电弹屏有一个加载状态 修改位置【voice_portal_v2.jsp】
		2.7.4#20220106-1 修改通话记录，修改呼叫方向、主被叫查询条件和分页方式
		2.7.4#20211231-1
			1.漏话列表点击处理时，处理人读取业务库中的坐席姓名。 修改文件：CallNoAnswerServlet.java
			2.修复分时话务统计中，不选技能组数据都为空。 修改文件:StatistcServiceSql.java
			3.报表统计中可以加导出不仅只有界面显示的数据，界面须传入参数allExport=1。
				修改文件：agentCallStat.jsp entCallStat.jsp groupCallStat.jsp hourCallStat.jsp serviceLevelReport.jsp wholeOperate.jsp
		2.7.4#20211229-2  
			1.修改企业未配置工单模板时，点击【新建工单】，页面提示【出现网路故障，请稍后再试】 修改文件 order-new.jsp
		2.7.4#20211229-1
			1.坐席来电未接示忙时弹窗提醒，是否重新示闲。 修改文件为ccbar_agent.js ccbar-agent-setting.jsp
			2.工单级联管理修改切换级联树表格控件。 修改文件为order-select-config.jsp treeTable.css
		2.7.4#20211224-1
			1.现场管理”→“座席工作情况”→“导出”，发现接话次数列，系统页面是0的，导出的报表单元格部分为空 修改文件为ExportServlet.java
		2.7.4#20211220-2
			1.修改bug通话录音管理→通话记录查询，筛选座席工号后，导出通话记录，400 – Bad Request  修改文件：cdr-query.jsp、ExportServlet.java
		2.7.4#20211220-1
			1.修改点击【工单完成】弹窗报Column 'DATA1' in field list is ambiguous的问题 修改文件OrderServlet.java。
			2.修改已完成的工单不能修改、未完成的工单修改保存后数据消失问题 修改文件order-info.jsp、form-creater.js、order-core.js、template-edit.jsp。
			3.修改从cc_busi_user表取坐席名称,原逻辑是从cc_user中取值 修改文件OrderDao.java
		2.7.4#20211216-1
			1.webcall权限控制
		2.7.4#20211215-1
			1.通话记录新增号码归属地字段
		2.7.4#20211124-1 
			1、修改工单管理导出工单时级联数据为ID的问题，修改的文件有ExportServlet.java
		2.7.4#20211123-1 
			1、将工单管理工单查询中级联条件为input输入框改为cascader级联框，修改的文件为order-query-list.jsp、order-query-finished-list.jsp、order-query-dealing-list.jsp、order-query-temp-list.jsp和OrderDao.java
			2、修改同一模板不同级联下拉框使用同一个级联树是产生的id重复bug，修改的文件为temp.jsp和form-creater.js
			3、修改完成工单查看时点击不了呼叫问题，修改文件custInfo.jsp
		2.7.4#20211118-3 1、优化流程流转配置下的步骤动作中增加是否发送消息单选框的代码，修改的位置有template-flow-config.jsp、OrderServlet.java
			2、在数据库cc_agent_order_action新增字段SEND_MSG 作用是标识是否发送信息
		2.7.4#20211118-2 
			1、添加工程师工单首页。
			2、添加配置项IS_ORDER_INDEX
			3、修改Constants.java OrderDao.java WorkbenchServlet.java portal_common.js 
			4、新增 portal-engineer.jsp echarts.min.js
			5、修改工单关联级联模板后，列表显示问题
		2.7.4#20211118-1 1、修改工单多级下拉问题；2、修改工单关联模板问题；3、新增录音批量导出；
		2.7.3#20211112-2 1、在流程流转配置下的步骤动作中增加是否发送消息单选框，勾选后流程执行到下一步时发送消息进行提醒
			修改的内容有template-flow-config.jsp、OrderServlet.java、Constants.java
		2.7.2#20211112-1
			1、首页新增右下角新消息弹窗提醒。
			2、提供消息弹窗信息接口,可指定用户接收消息。
			3、修改NoticeDao.java PortalCommonDao.java GlobalContextListener.java NoticeServlet.java
				notice-list.jsp notice-mgr.jsp portal_common.js portal-agent.jsp
			4、新增 ReceiveMessageService.java NoticeAlertServlet.java
			5、新增表CC_NOTICE_USER
		2.7.3#20211108-1 1、修改已完成工单不可更改在order-core.js的OrderCore.orderInfo方法中进行判断完成的工单隐藏保存按钮和不可更改工单内容
		2.7.2#20211027-1 1、工单查询导出限制；2、我的工单处理脚本优化；
		2.7.2#20210609-1
			1、工单模板配置添加级联下拉框。
			2、新增页面级联配置管理。
			3、修改OrderServlet.java OrderTempFlowServlet.java OrderTempServlet.java Orderdao.java
			order-my-temporary.jsp order-new.jsp form-create.jsp temp.jsp template-edit.jsp
			template-flow-config.jsp template-list.jsp charts.jsp mycharts.js form-creater.js order-core.js
			4、新增OrderRoleServlet.java OrderSelectConfigServlet.java OrderSelectConfigDao.java 
			order-servlet-config.jsp order-selectConf-edit.jsp cascader-select.jsp 
    	2.1.1#20181019
    		1、
    		2、
    	2.1.1#20181010
    		1、
    		2、
    	2.1.2#20181102
    		1、修改坐席工作日志bug
    	2.1.2#20181108
    		1、修改通话记录中，无法查询呼出的客户号码
   		2.0#20190218
    		1、增加全媒体统计报表
    	2.0#20190226
    		1、解决全媒体报表导出结果与查询不符合的问题。
    	2.0#20190521
    		1、全媒体服务记录中增加服务小结字段，SERVICE_RESULT
    		alter table CC_MEDIA_RECORD add SERVICE_RESULT varchar2(1000);
			comment on column CC_MEDIA_RECORD.SERVICE_RESULT is '服务小结';
    	2.0#20191120
    		1、添加话务报表
    		2、添加全媒体报表
    </versionHistory>
</application>
