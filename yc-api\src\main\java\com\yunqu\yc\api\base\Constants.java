package com.yunqu.yc.api.base;


import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.string.StringUtils;


public class Constants {
	
	public final static int SYSTEM_CODE_200 = 200;

	public final static String SYSTEM_CODE_200_MSG = "Success"; 

	public final static int SYSTEM_CODE_500 = 500; 

	public final static String SYSTEM_CODE_500_MSG = "System error"; 

	public final static int SYSTEM_CODE_510 = 510;

	public final static String SYSTEM_CODE_510_MSG = "Command not exist";

	public final static int SYSTEM_CODE_511 = 511; 

	public final static String SYSTEM_CODE_511_MSG = "Parameter error"; 

	public final static int SYSTEM_CODE_512 = 512; 

	public final static String SYSTEM_CODE_512_MSG = "Sign error";

	public final static int SYSTEM_CODE_513 = 513; 

	public final static String SYSTEM_CODE_513_MSG = "Session timeout";

	public final static int SYSTEM_CODE_514 = 514; 

	public final static String SYSTEM_CODE_514_MSG = "System no response";

	public final static int SYSTEM_CODE_515 = 515; 

	public final static String SYSTEM_CODE_515_MSG = "Unknow error"; 
	
	public final static int SYSTEM_CODE_516 = 516; 

	public final static String SYSTEM_CODE_516_MSG = "EntId not found"; 
	
	public final static int SYSTEM_CODE_404 = 404; 

	public final static String SYSTEM_CODE_404_MSG = "GateServer not fount"; 
	
	public final static String GW_LOGGER_NAME = "yc-gateway";
	
	public final static String APP_NAME = "yc-api";
	
	public final static String WRITER_DS_NAME_ONE = "yc-write-ds-1";
	
	public final static String WRITER_DS_NAME_TOW = "yc-write-ds-2";
	
	public final static String READ_DS_NAME = "yc-read-ds";
	
	public final static String GATE_SERVER  = "<GateServer> ";
	
	public final static String GATE_CLIENT  = "<GateClient> ";
	
	public final static String MQ_LOGGER_NAME = "yc-api-mq";
	
	private final static String STAT_DB = "stat";
	
	private final static AppContext context = AppContext.getContext("yc-api");
	
	
	public   final static String MARSMGR_DEAMON_NAME  = "MARSMGR-DEAMON-NAME";
	
	public static String getStatSchema(){
		return context.getProperty("STAT_DB",STAT_DB);
	}
	
	public static String getIvrAddrs(){
		return context.getProperty("IVR_ADDR","");
	}
	
	
	public static String getStatTable(String tableName){
		return context.getProperty("STAT_DB",STAT_DB) +"."+tableName;
	}
	
	
	public static String getServiceList(){
		return context.getProperty("TASK_SERVICE_LIST","");
	}

	private static ThreadGroup  appThreadGroup;

	public static synchronized ThreadGroup getAppThreadGroup(){
		if(appThreadGroup != null){
			return appThreadGroup;
		}
		appThreadGroup = new ThreadGroup(APP_NAME);
		return appThreadGroup;
	}
	
	/**
	 * 是否开启cc_task_obj 分表
	 */
	public static boolean objSplitFlag(){
		//根据任务分表改为全局配置，需要升级easitelin-console
		return "1".equals(ServerContext.getProperties("TASK_OBJ_SPLIT_TABLE_FLAG", "0"));
	}
	
	/**
	 * 获取任务对象的真实主叫
	 * @return
	 */
	public static String getRealCallerService(){
		return context.getProperty("GET_CALLER_SERVICE","");
	}
	
	/**
	 * 获取任务的真实被叫
	 * @return
	 */
	public static String getRealCalledService(){
		return context.getProperty("GET_CALLED_SERVICE","");
	}
	
	/**
	 * 获取名敏感词的检查方式
	 * @return
	 */
	public static String getSensitiveCheckMode(){
		return context.getProperty("SENSITIVE_CHECK_MODE","normal");
	}
	
	
	public static int  getTaskBatchCount(){
		return Integer.parseInt(context.getProperty("TASK_BATCH_COUNT","50"));
	}
	

	/**
	 * 如果有振铃时间，多少秒后写漏话
	 * @return
	 */
	public static int  getNoanswerAlteringTime(){
		return Integer.parseInt(context.getProperty("NOANSWER_ALTERING_TIME","5"));
	}
	
	/**
	 * 客户资料数据加密模式,1 明文  2 base64编码
	 * @return
	 */
	public static boolean isBase64(){
		String dataMode =  context.getProperty("CUST_DATA_MODE","1");
		return "2".equals(dataMode);
	}
	
	/**
	 * 当前是否是服务标志
	 * @return
	 */
	public static  boolean isServerFlag(){
		String serverFlag = context.getProperty("SERVER_FLAG", "false");
		return ("true".equalsIgnoreCase(serverFlag));
	}
	
	/**
	 * 呼叫红名单
	 * @return
	 */
	public static  boolean callRedList(){
		String flag = context.getProperty("CALL_RED_LIST", "false");
		return ("true".equalsIgnoreCase(flag));
	}
	
	/**
	 * 互损是否重呼
	 * @return
	 */
	public static  boolean isRecall(){
		String flag = context.getProperty("CALL_AGENT_FAIL_RECALL", "false");
		return ("true".equalsIgnoreCase(flag));
	}
	
	public static   boolean  isDaemon(){
		String _mgrName = StringUtils.trimToEmpty(CacheManager.getMemcache().get(Constants.MARSMGR_DEAMON_NAME));
		if(_mgrName.equalsIgnoreCase(ServerContext.getNodeName())){
			return true;
		}
		return isServerFlag();
	}
	
	/**
	 * 话批是否进行鉴权
	 * @return
	 */
	public static boolean  isFeeAuthen(){
		String authen = context.getProperty("FEE_AUTHEN_FLAG", "true");
		if(!"false".equalsIgnoreCase(authen)){
			return true;
		}
		return false;
	}

	/**
	 * 调度线程数
	 * @return
	 */
	public static int getEventThreadCount(){
		try {
			int eventThreadCount = Integer.parseInt(context.getProperty("EVENT_THREAD_COUNT", "50"));
			if(eventThreadCount < 0){
				eventThreadCount = 50;
			}
			if(eventThreadCount >300){
				eventThreadCount = 300;
			}
			return eventThreadCount;
		} catch (Exception e) {
		}
		return 50;
	}

	/**
	 * 调度线程池测试
	 * @return
	 */
	public static boolean eventThreadTest(){
		return "true".equals(context.getProperty("EVENT_THREAD_TEST","false"));
	}

//	public final static int SYSTEM_CODE_200 = 200;
//
//	public final static String SYSTEM_CODE_200_MSG = "成功"; 
//
//	public final static int SYSTEM_CODE_500 = 500; 
//
//	public final static String SYSTEM_CODE_500_MSG = "系统错误"; 
//
//	public final static int SYSTEM_CODE_510 = 510;
//
//	public final static String SYSTEM_CODE_510_MSG = "Command不存在";
//
//	public final static int SYSTEM_CODE_511 = 511; 
//
//	public final static String SYSTEM_CODE_511_MSG = "输入参数有误"; 
//
//	public final static int SYSTEM_CODE_512 = 512; 
//
//	public final static String SYSTEM_CODE_512_MSG = "数字签名有误";
//
//	public final static int SYSTEM_CODE_513 = 513; 
//
//	public final static String SYSTEM_CODE_513_MSG = "客户端时间戳超时（接口时间戳对比服务器时间超过10分钟）";
//
//	public final static int SYSTEM_CODE_514 = 514; 
//
//	public final static String SYSTEM_CODE_514_MSG = "系统正在维护中";
//
//	public final static int SYSTEM_CODE_515 = 515; 
//
//	public final static String SYSTEM_CODE_515_MSG = "未知错误"; 
//	
//	public final static String GW_LOGGER_NAME = "yc-gateway";
//	
//	public final static String DS_NAME = "yc-api-ds";
//	
//	public final static String APP_NAME = "yc-api";

	
}