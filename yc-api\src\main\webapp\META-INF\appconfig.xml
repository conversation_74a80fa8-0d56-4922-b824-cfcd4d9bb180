<?xml version="1.0" encoding="UTF-8"?>
<config>
   <param index="1"  key="SERVER_FLAG" name="磐石接口服务标志" type="radio"  items="false:不启用,true:启用 " description="取值为true为磐石的接口服务，该配置具备唯一性，MGR节点设置为启用" value="false"></param>
   <param index="2"  key="ActiveMQ_ADDR" name="ActiveMQ地址" type="String" description="3.0以后的版本无需配置" value=""></param>
   <param index="3"  key="BROKER_ADDR" name="MQ_BROKER所在地址段" type="String" description="Broker所在IP地址段，如:10. 或 172.，获取到的本地IP地址必须唯一" value=""></param>
   <param index="4"  key="STAT_DB" name="统计库数据库名" type="String" description="统计库数据库名" value="stat"></param>
   <param index="5"  key="IVR_ADDR" name="IVR访问地址" type="String" description="用户通过http地址来启动IVR流程，格式：ip:port 多个地址采用；进行分割" value="http://ip:port/"></param>
   <param index="6"  key="DS_ADDR" name="DS的HTTP地址" type="String" description="调用DS的HTTP地址，用于发起全程监控" value="http://ip:port/"></param>
   <param index="7"  key="TASK_BATCH_COUNT" name="OCS每次获取任务记录数" type="int" description="OCS每次获取任务的记录数，根据现场的任务调度性能来配置" value="200"></param>
   <param index="8"  key="TASK_VERIFIY_SERVICE_LIST" name="任务数据有效性验证服务列表" type="String" description="通过任务对外呼任务数据有效性进行验证，多个服务采用逗号[,]分隔" value=""></param>
   <param index="9"  key="GET_CALLER_SERVICE" name="获得任务执行对象外呼主叫" type="String" description="填写服务名，在外呼时候通过服务获取外呼主叫号码,如服务名：TASK_GET_CALLER_SERVICE" value=""></param>
   <param index="10"  key="GET_CALLED_SERVICE" name="获得任务执行对象真实客户号码" type="String" description="填写服务名，在外呼时候通过服务获取外呼真实客户号码" value=""></param>
   <param index="11"  key="RECORD_DIR" name="录音文件下载目录" type="String" description="填写mars所在机器的路径下载目录，如:/home/<USER>/" value=""></param>
   <param index="12"  key="BUSI_DB" name="缺省业务库" type="String" description="缺省业务库，用户数据无法归属到企业的时候，存放到缺省业务库" value="ycbusi1"></param>
   <param index="13"  key="CUST_DATA_MODE" name="客户数据加密模式" type="radio"  items="1:明文,2:BASE64编码 " description="机器人外呼场景，客户信息数据加密模式,1:明文,2:BASE64编码" value="2"></param>
   <param index="14"  key="SENSITIVE_CHECK_MODE" name="敏感词检查模式" type="radio"  items="normal:关键词匹配,strict:智能匹配算法 " description="检查文本中含有系统和企业敏感词进行匹配" value="normal"></param>
   <param index="15"  key="FEE_AUTHEN_FLAG" name="预付费鉴权" type="radio" items="true:启用,false:不启用"  description="启用，对预付费企业进行计费鉴权" value="false"></param>
   <param index="16"  key="PETRA_DATA_SYNC_MODE" name="MARS同步磐石数据模式" type="radio" items="MQ:MQ,REDIS:REDIS"  description="缺省采用MQ模式进行同步" value="MQ"></param>
   <param index="17"  key="CALL_AGENT_FAIL_RECALL" name="呼损重呼" type="radio" items="true:重呼,false:不重呼"  description="OCS呼通用户后转接坐席失败名单是否重呼" value="false"></param>
   <param index="18"  key="CALL_RED_LIST" name="电销允许呼叫红名单" type="radio" items="true:允许,false:不允许"  description="电销任务是否运行呼叫红名单，缺省：不允许" value="false"></param>
   <param index="21"  key="NOANSWER_ALTERING_TIME" name="未接来电振铃时间" type="String"  description="坐席振铃后，用户主动挂机，如果振铃时间大于这个配置值，则属于未接来电" value="5"></param>
   <param index="22"  key="EVENT_THREAD_COUNT" name="调度线程数" type="String"  description="调度线程数不能超过300" value="50"></param>
   <param index="23"  key="EVENT_THREAD_TEST" name="调度线程池测试" type="radio" items="true:启用,false:不启用"  description="启用后，需登录业务平台，可访问/yc-api/threadPoolTest进行测试" value="false"></param>

</config>
